# -*- coding: utf-8 -*-
"""
Git内存泄漏测试脚本
用于测试git模块的内存使用情况和验证修复效果
"""

import gc
import time
import threading
from lsr.anim_lib.utility.memory_manager import MEMORY_MANAGER


def test_git_controller_memory():
    """测试GitActionController的内存使用"""
    print("=== Testing GitActionController Memory Usage ===")
    
    from lsr.anim_lib.team_action.git.controller import GitActionController
    
    initial_memory = MEMORY_MANAGER.get_memory_usage()
    print("Initial memory: %s" % initial_memory)
    
    controllers = []
    
    # 创建多个GitActionController实例
    for i in range(10):
        controller = GitActionController()
        # 模拟设置repo
        try:
            controller.current_repo = "C:/temp/test_repo"  # 假设的路径
        except:
            pass  # 忽略错误，我们只是测试内存
        controllers.append(controller)
        
        if i % 5 == 0:
            current_memory = MEMORY_MANAGER.get_memory_usage()
            print("After creating %d controllers: %s" % (i+1, current_memory))
    
    # 清理controllers
    for controller in controllers:
        if hasattr(controller, '_cleanup_repo'):
            controller._cleanup_repo()
    
    controllers.clear()
    gc.collect()
    
    final_memory = MEMORY_MANAGER.get_memory_usage()
    print("Final memory after cleanup: %s" % final_memory)


def test_subprocess_memory():
    """测试subprocess的内存使用"""
    print("\n=== Testing Subprocess Memory Usage ===")
    
    from lsr.anim_lib.team_action.git.controller import GitActionController
    
    controller = GitActionController()
    
    initial_memory = MEMORY_MANAGER.get_memory_usage()
    print("Initial memory: %s" % initial_memory)
    
    # 模拟多次git命令执行
    for i in range(5):
        try:
            # 使用简单的命令来测试
            cmds = ["echo test_%d" % i]
            for line in controller.run_progress(cmds):
                pass  # 消费生成器
        except Exception as e:
            print("Command %d failed: %s" % (i, e))
        
        current_memory = MEMORY_MANAGER.get_memory_usage()
        print("After command %d: %s" % (i+1, current_memory))
    
    # 清理
    if hasattr(controller, '_cleanup_repo'):
        controller._cleanup_repo()
    
    gc.collect()
    
    final_memory = MEMORY_MANAGER.get_memory_usage()
    print("Final memory after cleanup: %s" % final_memory)


def test_thread_memory():
    """测试线程的内存使用"""
    print("\n=== Testing Thread Memory Usage ===")
    
    from lsr.anim_lib.manager.thread import ThreadManager
    
    initial_memory = MEMORY_MANAGER.get_memory_usage()
    print("Initial memory: %s" % initial_memory)
    
    # 启动线程
    ThreadManager.init()
    time.sleep(5)  # 让线程运行一段时间
    
    running_memory = MEMORY_MANAGER.get_memory_usage()
    print("Memory with threads running: %s" % running_memory)
    
    # 停止线程
    ThreadManager.terminated_all_threads()
    time.sleep(2)  # 等待线程完全停止
    
    gc.collect()
    
    final_memory = MEMORY_MANAGER.get_memory_usage()
    print("Final memory after thread cleanup: %s" % final_memory)


def stress_test_git_operations():
    """压力测试git操作"""
    print("\n=== Git Operations Stress Test ===")
    
    from lsr.anim_lib.team_action.git.controller import GitActionController
    
    MEMORY_MANAGER.start_monitoring(interval=5)
    
    initial_memory = MEMORY_MANAGER.get_memory_usage()
    print("Initial memory: %s" % initial_memory)
    
    controller = GitActionController()
    
    # 模拟大量git操作
    for cycle in range(3):
        print("Stress test cycle %d" % (cycle + 1))
        
        for i in range(20):
            try:
                # 模拟各种git命令
                cmds = [
                    "echo 'test command %d'" % i,
                    "echo 'another test %d'" % i
                ]
                for cmd in cmds:
                    for line in controller.run_progress([cmd]):
                        pass
                        
                # 模拟repo操作
                if hasattr(controller, 'validate_git_repo'):
                    controller.validate_git_repo("C:/temp/fake_repo_%d" % i)
                    
            except Exception as e:
                print("Operation %d failed: %s" % (i, e))
        
        # 每个周期后强制清理
        MEMORY_MANAGER.cleanup_git_objects()
        MEMORY_MANAGER.force_garbage_collection()
        
        cycle_memory = MEMORY_MANAGER.get_memory_usage()
        print("Memory after cycle %d: %s" % (cycle + 1, cycle_memory))
        
        time.sleep(2)
    
    # 最终清理
    if hasattr(controller, '_cleanup_repo'):
        controller._cleanup_repo()
    
    MEMORY_MANAGER.stop_monitoring()
    
    final_memory = MEMORY_MANAGER.get_memory_usage()
    print("Final memory after stress test: %s" % final_memory)
    
    print("\n" + MEMORY_MANAGER.get_memory_report())


def run_all_tests():
    """运行所有测试"""
    print("Starting Git Memory Leak Tests...")
    print("=" * 50)
    
    try:
        test_git_controller_memory()
        test_subprocess_memory()
        test_thread_memory()
        stress_test_git_operations()
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        print("\nFinal Memory Report:")
        print(MEMORY_MANAGER.get_memory_report())
        
    except Exception as e:
        print("Test error: %s" % str(e))
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
