# -*- coding: utf-8 -*-
"""
内存管理工具模块
用于监控和清理anim-lib中的内存使用，特别是git相关的内存泄漏
"""

import gc
import sys
import threading
import time
import weakref
from collections import defaultdict

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("[MemoryManager] psutil not available, memory monitoring will be limited")


class MemoryManager(object):
    """内存管理器，用于监控和清理内存"""
    
    def __init__(self):
        self._tracked_objects = weakref.WeakSet()
        self._memory_stats = defaultdict(list)
        self._monitoring = False
        self._monitor_thread = None
        
    def track_object(self, obj, name=None):
        """跟踪对象，用于内存监控"""
        try:
            self._tracked_objects.add(obj)
            if name:
                setattr(obj, '_memory_manager_name', name)
        except TypeError:
            # 某些对象不能被weakref跟踪
            pass
    
    def get_memory_usage(self):
        """获取当前内存使用情况"""
        if HAS_PSUTIL:
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                return {
                    'rss': memory_info.rss / 1024 / 1024,  # MB
                    'vms': memory_info.vms / 1024 / 1024,  # MB
                    'percent': process.memory_percent()
                }
            except Exception as e:
                print("[MemoryManager] Error getting memory info: %s" % str(e))
        
        # 备用方法
        return {
            'objects': len(gc.get_objects()),
            'tracked': len(self._tracked_objects)
        }
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        collected = 0
        for generation in range(3):
            collected += gc.collect(generation)
        
        print("[MemoryManager] Garbage collection completed, collected %d objects" % collected)
        return collected
    
    def cleanup_git_objects(self):
        """清理git相关对象"""
        from lsr.anim_lib.data import constant
        
        cleanup_count = 0
        try:
            # 清理git controller
            if hasattr(constant, 'INSTANCE_TEAM_ACTION_CTRL') and constant.INSTANCE_TEAM_ACTION_CTRL:
                if hasattr(constant.INSTANCE_TEAM_ACTION_CTRL, '_cleanup_repo'):
                    constant.INSTANCE_TEAM_ACTION_CTRL._cleanup_repo()
                    cleanup_count += 1
            
            # 清理git相关的全局变量
            git_vars = [
                'KEYPATH_GIT_REMOTE_UPDATED_FOOTAGES',
                'KEYPATH_GIT_REMOTE_UPDATED_ITEMS', 
                'KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES',
                'KEYPATH_GIT_LOCAL_UPDATED_ITEMS'
            ]
            
            for var_name in git_vars:
                if hasattr(constant, var_name):
                    var_value = getattr(constant, var_name)
                    if isinstance(var_value, list):
                        var_value.clear()
                        cleanup_count += 1
            
            print("[MemoryManager] Cleaned up %d git objects" % cleanup_count)
            
        except Exception as e:
            print("[MemoryManager] Error cleaning git objects: %s" % str(e))
        
        return cleanup_count
    
    def start_monitoring(self, interval=30):
        """开始内存监控"""
        if self._monitoring:
            return
            
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
        print("[MemoryManager] Started memory monitoring with %ds interval" % interval)
    
    def stop_monitoring(self):
        """停止内存监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        print("[MemoryManager] Stopped memory monitoring")
    
    def _monitor_loop(self, interval):
        """内存监控循环"""
        while self._monitoring:
            try:
                memory_info = self.get_memory_usage()
                current_time = time.time()
                
                # 记录内存统计
                for key, value in memory_info.items():
                    self._memory_stats[key].append((current_time, value))
                    # 只保留最近100个记录
                    if len(self._memory_stats[key]) > 100:
                        self._memory_stats[key] = self._memory_stats[key][-100:]
                
                # 检查内存使用是否过高
                if HAS_PSUTIL and 'percent' in memory_info:
                    if memory_info['percent'] > 80:  # 内存使用超过80%
                        print("[MemoryManager] High memory usage detected: %.1f%%" % memory_info['percent'])
                        self.cleanup_git_objects()
                        self.force_garbage_collection()
                
                time.sleep(interval)
                
            except Exception as e:
                print("[MemoryManager] Monitor loop error: %s" % str(e))
                time.sleep(interval)
    
    def get_memory_report(self):
        """获取内存使用报告"""
        report = []
        report.append("=== Memory Manager Report ===")
        
        current_memory = self.get_memory_usage()
        for key, value in current_memory.items():
            if isinstance(value, float):
                report.append("%s: %.2f MB" % (key, value))
            else:
                report.append("%s: %s" % (key, value))
        
        report.append("Tracked objects: %d" % len(self._tracked_objects))
        
        if self._memory_stats:
            report.append("\n=== Memory History ===")
            for key, history in self._memory_stats.items():
                if history:
                    latest = history[-1][1]
                    if isinstance(latest, float):
                        report.append("%s (latest): %.2f" % (key, latest))
                    else:
                        report.append("%s (latest): %s" % (key, latest))
        
        return "\n".join(report)


# 全局内存管理器实例
MEMORY_MANAGER = MemoryManager()


def cleanup_memory():
    """便捷函数：清理内存"""
    MEMORY_MANAGER.cleanup_git_objects()
    return MEMORY_MANAGER.force_garbage_collection()


def start_memory_monitoring(interval=30):
    """便捷函数：开始内存监控"""
    MEMORY_MANAGER.start_monitoring(interval)


def stop_memory_monitoring():
    """便捷函数：停止内存监控"""
    MEMORY_MANAGER.stop_monitoring()


def get_memory_report():
    """便捷函数：获取内存报告"""
    return MEMORY_MANAGER.get_memory_report()
