# Git模块内存泄漏修复指南

## 问题概述

在anim-lib包中，git模块存在几个可能导致Maya内存不断增加的问题：

1. **subprocess.Popen进程未正确清理**
2. **GitPython Repo对象未释放**
3. **线程中的异常处理不当**
4. **缺乏内存监控和清理机制**

## 修复内容

### 1. GitActionController类修复

#### 问题
- `run_progress`方法中的subprocess.Popen进程未正确清理
- GitPython Repo对象在切换仓库时未释放
- `validate_git_repo`方法创建临时Repo对象未清理

#### 解决方案
- 添加了完整的资源清理机制
- 实现了`_cleanup_repo`方法
- 在`run_progress`中添加了try-finally块确保进程清理
- 修复了`validate_git_repo`中的内存泄漏

### 2. 线程管理优化

#### 问题
- GitStatusWatchDog线程异常处理不当
- 线程未设置为守护线程
- 缺乏错误计数和自动恢复机制

#### 解决方案
- 改进了异常处理，添加错误计数机制
- 设置线程为守护线程，主程序退出时自动结束
- 增加了睡眠时间，减少CPU占用和git操作频率
- 添加了线程安全的终止机制

### 3. 内存管理器

#### 新增功能
- 实时内存监控
- 自动垃圾回收
- Git对象清理
- 内存使用报告

## 使用方法

### 自动启用
修复后的代码会自动启用内存管理：
- 程序启动时自动开始内存监控
- 程序关闭时自动清理资源
- 内存使用过高时自动触发清理

### 手动控制
```python
from lsr.anim_lib.utility.memory_manager import (
    cleanup_memory, 
    start_memory_monitoring, 
    stop_memory_monitoring,
    get_memory_report
)

# 手动清理内存
cleanup_memory()

# 开始内存监控（60秒间隔）
start_memory_monitoring(60)

# 停止内存监控
stop_memory_monitoring()

# 获取内存报告
print(get_memory_report())
```

### 测试验证
运行测试脚本验证修复效果：
```python
from lsr.anim_lib.utility.git_memory_test import run_all_tests
run_all_tests()
```

## 主要改进点

### 1. 进程管理
- 添加了进程超时机制
- 实现了强制终止机制
- 确保所有管道正确关闭

### 2. 资源清理
- GitPython对象的正确释放
- 文件句柄的及时关闭
- 缓存的定期清理

### 3. 错误处理
- 增强的异常捕获
- 错误计数和自动恢复
- 详细的错误日志

### 4. 性能优化
- 减少git操作频率
- 优化线程睡眠时间
- 智能的内存监控

## 监控指标

### 内存使用
- RSS内存（物理内存）
- VMS内存（虚拟内存）
- 内存使用百分比

### 对象跟踪
- Python对象数量
- 跟踪对象数量
- 垃圾回收统计

## 注意事项

1. **psutil依赖**：如果系统没有安装psutil，内存监控功能会受限，但基本清理功能仍然可用

2. **线程安全**：所有清理操作都是线程安全的，可以在任何时候调用

3. **性能影响**：内存监控会有轻微的性能开销，但相比内存泄漏的影响微不足道

4. **兼容性**：修复保持了原有API的兼容性，不会影响现有代码

## 故障排除

### 如果内存仍然增长
1. 检查是否有其他模块创建了Git对象
2. 运行测试脚本确认修复是否生效
3. 查看内存报告分析具体问题

### 如果线程无法正常停止
1. 检查线程是否设置了do_run=False
2. 确认没有其他代码持有线程引用
3. 使用强制终止机制

### 如果git操作失败
1. 检查git路径配置
2. 确认仓库路径有效
3. 查看错误日志确定具体问题

## 建议

1. **定期监控**：建议在开发和测试环境中启用内存监控
2. **主动清理**：在长时间运行的会话中定期调用cleanup_memory()
3. **日志记录**：关注内存管理器的日志输出，及时发现问题
4. **版本控制**：建议将修复后的代码纳入版本控制，确保团队使用统一版本
