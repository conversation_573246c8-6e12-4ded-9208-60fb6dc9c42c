{"cgame_exporter": {"command": "PY:import lsr.maya.cgame.command as cmd; cmd.cgame_export_fbx()", "icon": "cgame_exporter.png", "tag": ["rig", "ui"]}, "cgame_anim_pose_search": {"command": "PY:import lsr.maya.cgame.command as cmd; cmd.cgame_anim_pose_search()", "icon": "anim_pose_search.png", "tag": ["rig", "ui"]}, "CGame_ActorUI": {"command": "PY:import lsr.maya.anim_tools.commerce_actor_tool.main as main;main.Launch_CGameActorUI()", "icon": "CGameBodyTransform.png", "tag": ["rig", "ui"]}, "S_Studio_PoseLib": {"command": "PY:import lsr.maya.anim_tools.S_Studio_PoseLib.EIAnimUI as main;main.EIAnimUI().show()", "icon": "poselib.png", "tag": ["anim", "ui"]}, "BonePair_Config": {"command": "PY:import lsr.maya.anim_tools.bone_constraint.main as main;main.show()", "icon": "BonePairConstraint.png", "tag": ["anim", "ui"]}, "S_Studio_Exp": {"command": "PY:from lsr.maya.anim_tools.S_Studio_Export import Tool;Tool.ExportAnimationTool().showUI()", "icon": "ss_export.png", "tag": ["anim", "ui"]}, "CGame_Commerce_EXP": {"command": "PY:import lsr.maya.anim_tools.commerce_actor_tool.main as main;main.Launch_CGameCGExportToolUI()", "icon": "export.png", "tag": ["anim", "ui"]}, "CGame_ExportTool_UI": {"command": "PY:import lsr.maya.anim_tools.commerce_actor_tool.main as main;main.Launch_CGame_ExportTool_UI()", "icon": "export.png", "tag": ["anim", "ui"]}, "CGame_AnimationConvert_UI": {"command": "PY:from lsr.maya.anim_tools.transfer_animation import batch_processing;batch_processing.CGame_AnimationConvert_UI.showUI()", "icon": "pubg_ac.png", "tag": ["anim", "ui"]}, "CGame_Batch_ExportTool_UI": {"command": "PY:import lsr.maya.anim_tools.fbx_exporter.batch_exporter_view as main;win = main.Batch_AnimExporterWindow.launch();win.setAcceptDrops(True)", "icon": "export.png", "tag": ["anim", "ui"]}, "CGame_Retargeter_UI": {"command": "PY:import lsr.maya.cgame.retargeter.retarget_view as main;main.AnimRetargeterWindow.launch()", "icon": "lsr_retarget.png", "tag": ["anim", "ui"]}, "CGame_switch_slot_name_UI": {"command": "PY:import lsr.maya.cgame.switch_slot_name_tool.main as switch_slot_main;switch_slot_main.main()", "icon": "switch_slot_name_tool.png", "tag": ["anim", "ui"]}, "CGame_face_camera_UI": {"command": "from lsr.maya.cgame.face_camera import mainUI;mainUI.launch()", "icon": "face_camera.png", "tag": ["anim", "ui"]}, "captureCamera": {"command": "PY:from lsr.maya.cgame.capture_camera import ui;ui.baseUI.showUI()", "icon": "captureCamera.png", "tag": ["anim", "ui"]}, "curve_filter": {"command": "PY:from lsr.maya.cgame.curve_filter import CurveFilter as Filter;Filter.main()", "icon": "curve_filter.png", "tag": ["anim", "ui"]}, "CGame_keyframeEdt": {"command": "PY:from lsr.maya.cgame.keyfram_editor_tool import keyframeEditorUI;keyframeEditorUI.launch()", "icon": "keyframe.png", "tag": ["anim", "ui"]}, "camera_mask_tool": {"command": "PY:from lsr.maya.cgame.camera_masks_tool.ui import main_window as camera_mask_win;camera_mask_win.launch()", "icon": "camera_mask_tool.png", "tag": ["anim", "ui"]}, "LSR_AnimToolkit": {"command": "PY:import lsr.maya.cgame.anim_toolkit.ui.main as anim_toolkit_main;anim_toolkit_main.launch()", "icon": "animkit.png", "tag": ["anim", "ui"], "priority": 2}, "copy_end_controller": {"command": "PY:from lsr.maya.cgame.copy_end_controller.ui import main_window as copy_end_win;copy_end_win.launch()", "icon": "copy_end_controller.png", "tag": ["anim", "ui"]}, "select_cache_data": {"command": "PY:from lsr.maya.cgame.select_cache_data import cache as cache_win;cache_win.launch()", "icon": "select_cache_data.png", "tag": ["anim", "ui"]}}