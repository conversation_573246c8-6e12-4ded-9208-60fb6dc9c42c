{"LSR": [{"action": "LSR_AnimToolkit", "imageOverlayLabel": "cgame", "priority": 2}], "LSR_Classic": [{"action": "cgame_exporter", "imageOverlayLabel": ""}, {"action": "cgame_anim_pose_search", "imageOverlayLabel": ""}, {"mark": "Rigging", "backgroundColor": [0.6, 0.15, 0.1]}, {"action": "Joint_StructureCheck", "imageOverlayLabel": "Bone", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"action": "CGame_ActorUI"}, {"action": "ClassicalSkinTool", "imageOverlayLabel": "Skin"}, {"separator": true}, {"mark": "<PERSON><PERSON>", "backgroundColor": [0.4, 0.7, 0.2]}, {"action": "RestoreObject"}, {"action": "IKFK_Switcher"}, {"action": "FilterCurves"}, {"action": "CGame_AnimationConvert_UI"}, {"action": "GimbalLock"}, {"action": "IKFK_Batch"}, {"action": "mirror_pose"}, {"action": "KeyframeReduction"}, {"action": "UnifySkin"}, {"action": "S_Studio_PoseLib"}, {"action": "Spring_Tool"}, {"action": "BonePair_Config"}, {"action": "rsCameraUI"}, {"action": "S_Studio_Exp"}, {"action": "CGame_ExportTool_UI"}, {"action": "CGame_Commerce_EXP", "imageOverlayLabel": "new", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"action": "CopyCamTool", "imageOverlayLabel": "CamCopy", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"separator": true}, {"mark": "Model", "backgroundColor": [0.9, 0.8, 0.3]}, {"action": "MiscModel"}, {"action": "Normal_Tool"}, {"action": "<PERSON><PERSON><PERSON><PERSON>_Design"}, {"separator": true}, {"mark": "Public", "backgroundColor": [0.6, 0.6, 0.9]}, {"action": "ImportToMaya"}, {"action": "Y_UP"}, {"action": "Z_UP"}, {"separator": true}, {"action": "CGame_Batch_ExportTool_UI", "imageOverlayLabel": "<PERSON><PERSON>"}, {"action": "CGame_Retargeter_UI", "imageOverlayLabel": "Retargeter"}, {"action": "CGame_switch_slot_name_UI", "imageOverlayLabel": "Material"}, {"action": "CGame_face_camera_UI", "imageOverlayLabel": "Face_Camera"}, {"action": "captureCamera", "imageOverlayLabel": ""}, {"action": "curve_filter", "imageOverlayLabel": ""}, {"action": "CGame_keyframeEdt", "imageOverlayLabel": "", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"action": "camera_mask_tool", "imageOverlayLabel": "", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"action": "copy_end_controller", "imageOverlayLabel": "", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}, {"action": "select_cache_data", "imageOverlayLabel": "", "overlayLabelBackColor": [0.1322, 0.3344, 0.0393, 1]}]}