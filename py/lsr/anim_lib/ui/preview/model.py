"""
This module is for creating a list model for animation clips and poses.
"""

import os
import fnmatch
from functools import partial

from Qt import QtCore, QtGui
from lsr.anim_lib.manager import SignalManager
from lsr.anim_lib.ui.preview.item import AnimLibFlipbookItem
from lsr.anim_lib.data import constant


class AnimBrowserModel(QtCore.QAbstractListModel):
    """ Model for the animation browser """

    def __init__(self, parent=None):
        super(AnimBrowserModel, self).__init__(parent=parent)
        self.flipbook_items = list()
        self.flipbook_items_dict = dict()
        self.footage_folders = list()
        self.flipbook_loaded_count = 0
        SignalManager.model_item_decorate_data.connect(partial(self.data_change_cb))

    def flipbook_removed_cb(self, flipbook, *args, **kwargs):
        self.remove_items([flipbook])

    def data_change_cb(self, index, *args, **kwargs):
        self.dataChanged.emit(index, index, [QtCore.Qt.DecorationRole])

    def rowCount(self, index=QtCore.QModelIndex(), *args, **kwargs):
        return self.flipbook_loaded_count

    # def data(self, index, role, *args, **kwargs):
    #     idx = index.row()
    #     if idx > len(self.footage_folders):
    #         return
    #     try:
    #         item = self.flipbook_items[idx]
    #     except IndexError:
    #         return
    #     if role == QtCore.Qt.DecorationRole:
    #         if item.currentIcon():
    #             return item.currentIcon()
    #     if role == QtCore.Qt.UserRole:
    #         return item
    #
    def get_user_add_pushbutton_index(self, *args, **kwargs):
        return_index = []
        for item in self.flipbook_items:
            item_tags = item.io_data["flipbook_tags"]
            if "tool" in item_tags:
                return_index.append(self.flipbook_items.index(item))
        return return_index
    #
    # def get_hidden_item_index_by_tags(self, user_tags, *args, **kwargs):
    #     return_index = []
    #     if user_tags:
    #         for item in self.flipbook_items:
    #             item_tags = item.io_data["flipbook_tags"]
    #             if any(item_tag in user_tags for item_tag in item_tags):
    #                 return_index.append(self.flipbook_items.index(item))
    #     return return_index
    #
    # def get_hidden_item_index_by_filter_pattern(self, pattern, *args, **kwargs):
    #     return_index = []
    #     item_names = {}
    #     if pattern:
    #         for item in self.flipbook_items:
    #             item_names[item.get_item_full_name()] = item
    #
    #         # wildcard search
    #         if "*" in pattern or "?" in pattern or "[" in pattern:
    #             filtered_items = fnmatch.filter(item_names.keys(), pattern)
    #             for item in filtered_items:
    #                 return_index.append(self.flipbook_items.index(item_names[item]))
    #         # greedy search
    #         else:
    #             for item in item_names:
    #                 if pattern in os.path.basename(item):
    #                     return_index.append(self.flipbook_items.index(item_names[item]))
    #     return return_index
    #
    # def get_item_by_path(self, path, *args, **kwargs):
    #     if os.path.normpath(path) in self.flipbook_items_dict:
    #         return self.flipbook_items_dict[os.path.normpath(path)]
    #     return False
    #
    # def remove_items(self, items, *args, **kwargs):
    #     # remove items from the list
    #     for item in items:
    #         for row in range(0, len(self.flipbook_items)):
    #             if self.flipbook_items[row] == item:
    #                 self.beginRemoveRows(QtCore.QModelIndex(), row, row)
    #                 self.flipbook_items.pop(row)
    #                 self.endRemoveRows()
    #                 break
    #         footage_path = item.io_data["image_sequence_folder"]
    #         if footage_path in self.footage_folders:
    #             self.footage_folders.remove(footage_path)
    #
    # def canFetchMore(self, index, *args, **kwargs):
    #     return self.flipbook_loaded_count <= len(self.footage_folders)
    #
    def stop_playing_all_items(self, *args, **kwargs):
        for item in self.flipbook_items:
            if item._timer:
                item._timer.stop()
                item.jumpToFrame(0)
                item._percent = 1
    #
    # def fetchMore(self, index, *args, **kwargs):
    #     remainder = len(self.footage_folders) - self.flipbook_loaded_count
    #     itemsToFetch = min(10, remainder)
    #
    #     start = self.flipbook_loaded_count
    #     end = self.flipbook_loaded_count + itemsToFetch
    #     # only load the thumbnail
    #     for i in self.flipbook_items[start:end]:
    #         i.load_thumbNail()
    #     self.beginInsertRows(QtCore.QModelIndex(), self.flipbook_loaded_count,
    #                          self.flipbook_loaded_count + itemsToFetch)
    #     self.endInsertRows()
    #     self.flipbook_loaded_count += itemsToFetch
    #
    def sort_item(self, date=False, name=False, *args, **kwargs):

        def __get_folder_create_date(item):
            return os.path.getctime(item.io_data["flipbook_sequence_folder"])

        def __get_folder_name(item):
            return os.path.basename(item.io_data["flipbook_sequence_folder"])

        sort_function = None
        if date:
            sort_function = __get_folder_create_date
        elif name:
            sort_function = __get_folder_name

        last_item = self.flipbook_items[-1]
        new_items = self.flipbook_items[:-1]
        new_items.sort(key=sort_function)
        new_items.append(last_item)

        self.flipbook_loaded_count = 0
        self.beginResetModel()
        self.flipbook_items = new_items
        self.endResetModel()

    def set_data_path(self, path, *args, **kwargs):
        # reset
        self.flipbook_items = []
        self.footage_folders = []
        self.flipbook_loaded_count = 0
        self.beginResetModel()

        # get all footage folder under given path
        if path.endswith("_footage"):
            self.footage_folders.append(path)
        else:
            for root, dirs, files in os.walk(path):
                for dir in dirs:
                    if dir.endswith("_footage"):
                        self.footage_folders.append(os.path.join(root, dir))

        # from given footage generate flipbook item
        for footage in self.footage_folders:

            # reset
            animation_data_file = None
            annotation_data_file = None
            annotation_data_patten = "*.apd"
            hik_fbx_file = None
            flipbook_thumbnail_file = None
            flipbook_sequence_folder = footage
            flipbook_tags = []

            # collect
            # animation_data_filei
            if os.path.isfile(os.path.join(footage, "data", constant.NAME_ANIMATION_GZIP)):
                animation_data_file = os.path.join(footage, "data", constant.NAME_ANIMATION_GZIP)
            if not animation_data_file:
                if os.path.isfile(footage.replace("_footage", ".apd")):
                    if not os.path.isdir(os.path.join(footage, "data")):
                        os.makedirs(os.path.join(footage, "data"))
                    new_dir = os.path.join(footage, "data") + "/" + os.path.basename(footage).replace("_footage",
                                                                                                      ".apd")
                    os.rename(footage.replace("_footage", ".apd"), new_dir)
                    animation_data_file = new_dir
            if not animation_data_file:
                if os.path.isdir(os.path.join(footage, "data")):
                    all_files = os.listdir(os.path.join(footage, "data"))
                    apd_files = [file for file in all_files if file.endswith(".apd")]
                    if apd_files:
                        animation_data_file = os.path.join(footage, "data", apd_files[0])
            # annotation_data_file
            find_annotation_result = [os.path.join(footage, "data", filename) for root, dirs, files in os.walk(footage)
                                      for
                                      filename in fnmatch.filter(files, annotation_data_patten)]
            if find_annotation_result:
                annotation_data_file = find_annotation_result[0]
            else:
                if os.path.isfile(footage.replace("_footage", ".apd")):
                    new_dir = os.path.join(footage, "data") + "/" + os.path.basename(footage).replace("_footage",
                                                                                                      ".apd")
                    os.rename(footage.replace("_footage", ".apd"), new_dir)
                    annotation_data_file = new_dir
                elif os.path.isfile(footage.replace("_footage", ".json")):
                    new_dir = os.path.join(footage, "data") + "/" + os.path.basename(footage).replace("_footage",
                                                                                                      ".json")
                    os.rename(footage.replace("_footage", ".json"), new_dir)
                    annotation_data_file = new_dir

            # hik_fbx_file
            if os.path.isfile(os.path.join(footage, "data", constant.NAME_HIK_FBX + ".fbx")):
                hik_fbx_file = os.path.join(footage, "data", constant.NAME_HIK_FBX + ".fbx")

            # flipbook_thumbnail_file
            for p in os.listdir(footage):
                if p.endswith("jpg"):
                    flipbook_thumbnail_file = os.path.join(footage, p)
                    break

            # collect image tag
            image_count = len([jpg for jpg in os.listdir(footage) if jpg.endswith("jpg")])
            if image_count == 1:
                flipbook_tags.append("pose")
            else:
                flipbook_tags.append("animation")
            if hik_fbx_file:
                flipbook_tags.append("hik")

            # if image data is broken
            if not flipbook_thumbnail_file:
                flipbook_thumbnail_file = constant.KEYPATH_BROKEN_ITEM_IMAGE
                flipbook_tags = ["error"]


            # inject
            io_data = {
                "animation_data_file": animation_data_file,
                "annotation_data_file": annotation_data_file,
                "hik_fbx_file": hik_fbx_file,
                "flipbook_thumbnail_file": flipbook_thumbnail_file,
                "flipbook_sequence_folder": flipbook_sequence_folder,
                "flipbook_tags": flipbook_tags
            }
            current_item = AnimLibFlipbookItem(io_data)
            current_footage = os.path.normpath(flipbook_sequence_folder)
            self.flipbook_items.append(current_item)
            if not current_footage in self.flipbook_items_dict:
                self.flipbook_items_dict[current_footage] = None
            self.flipbook_items_dict[current_footage] = current_item

        # additional place holder data for user create action
        animation_data_file = None
        annotation_data_file = None
        hik_fbx_file = None
        flipbook_thumbnail_file = None
        flipbook_tags = ["tool"]
        io_data = {
            "animation_data_file": animation_data_file,
            "annotation_data_file": annotation_data_file,
            "hik_fbx_file": hik_fbx_file,
            "flipbook_thumbnail_file": flipbook_thumbnail_file,
            "flipbook_sequence_folder": "anim_lib_tool_footage",
            "flipbook_tags": flipbook_tags
        }
        user_add_item = AnimLibFlipbookItem(io_data)
        user_add_item._frames.append(QtGui.QIcon(constant.KEYPATH_USER_ADD_ITEM_IMAGE))
        self.footage_folders.append("_")
        self.flipbook_items.append(user_add_item)
        self.endResetModel()
