#!/usr/bin/env ptuyhon
# -*- coding: utf-8 -*-

import maya.cmds as cmds
import maya.OpenMaya as OpenMaya

import lsr.maya.rig.rig_global as rg
from lsr.maya.standard.name import NodeName
from lsr.maya.nodezoo.node import Node
import lsr.maya.rig.quadruped_utils as qutil
from lsr.maya.nodezoo.node import TransformConstraint
import lsr.maya.animtools.fbx_exporter.export_utils as utils
from lsr.maya.animtools.mayaUtil import common


def create_root_motion(*args, **kwargs):
    """
        Create root motion
    Args:
        *args:
        **kwargs:

    Returns:
        None

    """
    OpenMaya.MGlobal.displayInfo(kwargs)
    translate = kwargs.get("translate", False)
    translateXYZ = kwargs.get("translateXYZ", [])
    rotate = kwargs.get("rotate", False)
    rotateXYZ = kwargs.get("rotateXYZ", [])
    re_animation = kwargs.get("re_animation_objects", [])
    re_animation_objects = []

    for joint in re_animation:
        if cmds.objExists(joint):
            re_animation_objects.append(joint)

    roots = cmds.ls(assemblies=True)

    for jnt in roots:
        if jnt in re_animation_objects:
            children = Node(jnt).get_children()
            re_animation_objects += [x.name for x in children]
    re_animation_objects = list(set(re_animation_objects))

    is_root_motion = kwargs.get("is_root_motion", False)
    is_stop_frame = kwargs.get("is_stop_frame", False)
    is_body_at_origin_point = kwargs.get("is_body_at_origin_point", False)

    # transform to custom attribute
    transform_to_custom_attribute = kwargs.get("transform_to_custom_attribute", False)

    # repairRotation
    is_repair_root_rotation = kwargs.get("is_repair_root_rotation", False)
    startAngle = kwargs.get("startAngle", 0.0)
    endAngle = kwargs.get("endAngle", 0.0)
    axis = kwargs.get("repairAxis", 1)
    axis = ['x', 'y', 'z'][axis]

    # repair rotation start / end frame
    is_repair_frame = kwargs.get("is_repair_frame")
    startFrame = kwargs.get("startFrame")
    endFrame = kwargs.get("endFrame")
    if cmds.objExists("foot_l"):
        FOOT_L = Node("foot_l")
    else:
        FOOT_L = None
    if cmds.objExists("foot_r"):
        FOOT_R = Node("foot_r")
    else:
        FOOT_R = None

    if not is_root_motion:
        return
    if not translate and not rotate:
        return
    try:
        cmds.refresh(suspend=True)

        # Enable parallel evaluation
        cmds.evaluationManager(mode="parallel")

        if is_root_motion:
            tempLocator = {}
            """
            tempLocator = {"root":{"root_TMP":{},"pointConstraint":[],"orientConstraint":[]}}
            """
            for joint in re_animation_objects:
                TMP = qutil.create_locator(joint, name="{}_TMP".format(Node(joint).name))[0]
                TMP.set_parent("world")
                skipTranslate = []
                skipRotate = []
                pointConstraint = []
                orientConstraint = []
                rotateTmp = []
                aimObject = []
                if joint == "root":
                    if translate:
                        tx, ty, tz = translateXYZ
                        if tx == 0:
                            skipTranslate.append('x')
                        if ty == 0:
                            skipTranslate.append('y')
                        if tz == 0:
                            skipTranslate.append('z')
                    if rotate:
                        rx, ry, rz = rotateXYZ
                        if rx == 0:
                            skipRotate.append("x")
                        if ry == 0:
                            skipRotate.append("y")
                        if rz == 0:
                            skipRotate.append("z")

                    rotateTmp = qutil.create_locator(TMP, name="{}_OrTMP".format(Node(joint).name))[0]
                    aimObject = qutil.create_locator(TMP, name="{}_upTMP".format(Node(joint).name))[0]
                    aimObject.tz.value = 50


                else:
                    pass
                tempLocator[joint] = {"TMP": TMP,
                                      "skipTranslate": skipTranslate,
                                      "skipOrient": skipRotate,
                                      "pointConstraint": pointConstraint,
                                      "orientConstraint": orientConstraint,
                                      "parentConstraint": [],
                                      "rotateTemp": rotateTmp,
                                      "scaleConstraint": [],
                                      "aimObject": aimObject
                                      }
            # snap position ,and constrain
            for key, tem in tempLocator.items():
                obj = tem.get("TMP")
                if key == 'root':
                    if translate:
                        if tem['skipTranslate']:
                            # tem['pointConstraint'] = obj.constrain("point", ['pelvis'], skip=tem["skipTranslate"],
                            tem['pointConstraint'] = obj.constrain("point", ['pelvis'], skip=tem["skipTranslate"],
                                                                   maintainOffset=False,
                                                                   weight=True)
                            # tem['pointConstraint'] = transformConstraint(Node('pelvis'), obj,
                            #                                              constraint_attr='t',
                            #                                              skipTranslate=tem["skipTranslate"])

                        else:
                            obj.constrain("point", ['pelvis'], skip=["y"],
                                          maintainOffset=False,
                                          weight=True).delete()
                            tem['pointConstraint'] = obj.constrain("point", ["pelvis"], maintainOffset=True,
                                                                   weight=True)

                            # tem['pointConstraint'] = transformConstraint(Node('pelvis'), obj,
                            #                                              constraint_attr='t')
                    if rotate:
                        if tem['skipOrient']:
                            # tem['orientConstraint'] = obj.constrain("orient", ['pelvis'], skip=tem["skipOrient"], mo=True,
                            #                                         w=True)
                            tem['orientConstraint'] = tem.get("rotateTemp").constrain("aim",
                                                                                      [tem.get("aimObject")],
                                                                                      worldUpObject="pelvis",
                                                                                      upVector=[0, 1, 0],
                                                                                      skip=tem["skipOrient"],
                                                                                      worldUpType='objectrotation',
                                                                                      maintainOffset=True,
                                                                                      worldUpVector=[0, 0, 0],
                                                                                      weight=True)
                            tem.get("aimObject").constrain("parent", ["pelvis"], weight=True, skipTranslate=['y'],
                                                           skipRotate=['x', 'y', 'z'],
                                                           maintainOffset=True)
                        else:
                            # tem['orientConstraint'] = obj.constrain("orient", ['pelvis'], mo=True, w=True)
                            tem['orientConstraint'] = tem.get("rotateTemp").constrain("aim",
                                                                                      tem.get("aimObject"),
                                                                                      worldUpObject="pelvis",
                                                                                      upVector=[0, 1, 0],
                                                                                      worldUpType='objectrotation',
                                                                                      maintainOffset=True,
                                                                                      worldUpVector=[0, 0, 0],
                                                                                      weight=True)

                else:
                    tem['parentConstraint'] = obj.constrain("parent", [key], maintainOffset=False, weight=True)
                    tem['scaleConstraint'] = obj.constrain("scale", [key], maintainOffset=False, weight=True)

            # minTime = int(cmds.playbackOptions(query=True, minTime=True))
            # maxTime = int(cmds.playbackOptions(query=True, maxTime=True))
            frameList = []
            for t in cmds.ls(type="animCurve"):
                value_list = cmds.keyframe(t, query=True)
                if value_list:
                    frameList.append(int(min(value_list)))
                    frameList.append(int(max(value_list)))
            minTime = min(set(frameList))
            maxTime = max(set(frameList))
            cmds.playbackOptions(edit=True, minTime=minTime)
            cmds.playbackOptions(edit=True, animationStartTime=minTime)
            bake_results_setting = {
                'simulation': True,
                'sampleBy': 1,
                'oversamplingRate': 1,
                'disableImplicitControl': True,
                'preserveOutsideKeys': True,
                'sparseAnimCurveBake': False,
                'removeBakedAttributeFromLayer': False,
                'removeBakedAnimFromLayer': False,
                'bakeOnOverrideLayer': False,
                'minimizeRotation': True,
                'controlPoints': False,
                'shape': False,
                # "hierarchy": "selected"
                # 'attribute': ['tx', 'ty', 'tz','rx',]
            }

            # bake template locator
            bake_objects = [tempLocator[x].get("TMP") for x in tempLocator.keys()]

            # bake root temp rotate
            if rotate:
                if translate:
                    tempLocator["root"].get('pointConstraint').pelvisW0.value = 1
                if is_repair_frame:
                    _minTime = startFrame
                    _maxTime = endFrame
                else:
                    _minTime = minTime
                    _maxTime = maxTime
                cmds.bakeSimulation(tempLocator["root"].get('rotateTemp'),
                                    time=(_minTime, _maxTime),
                                    oversamplingRate=1,
                                    disableImplicitControl=True,
                                    preserveOutsideKeys=True,
                                    sparseAnimCurveBake=False,
                                    removeBakedAttributeFromLayer=False,
                                    removeBakedAnimFromLayer=False,
                                    bakeOnOverrideLayer=False,
                                    minimizeRotation=True,
                                    controlPoints=False,
                                    shape=False, )

            # repair root rotation
            if is_repair_root_rotation:
                kwargs = {"startFrame": startFrame, "endFrame": endFrame}
                repairRootRotation(tempLocator["root"].get("rotateTemp"), startAngle, endAngle, axis,
                                   *args,
                                   **kwargs)

            cmds.refresh(force=True)

            if startAngle != 0:
                rootRotationAngle(tempLocator["root"].get("rotateTemp"), startAngle=startAngle)

            # bake root translation
            if translate:
                tempLocator["root"].get('pointConstraint').pelvisW0.value = 1

                # set auto root stop
                if is_stop_frame:
                    auto_stopping(minTime, maxTime, FOOT_L, FOOT_R, tempLocator,
                                  *args, **bake_results_setting)
                else:
                    cmds.bakeResults(tempLocator["root"].get('TMP'), time=(minTime, maxTime),
                                     attribute=['tx', 'ty', 'tz'],
                                     **bake_results_setting)

                animCurveNode = tempLocator["root"].get('TMP').ty.source_node
                if animCurveNode:
                    cmds.selectKey(clear=True)
                    # find time range -1 < 0
                    keys = []
                    for key, value in animCurveNode.get_keys_data().items():
                        if value['value'] < 0.0:
                            keys.append(int(key))
                    if keys:
                        keys_list = find_continuous_sequences(keys)
                        if len(keys_list) > 1:
                            for key in keys_list:
                                cmds.selectKey(animCurveNode, addTo=True, keyframe=True, time=(key[0], key[-1]))
                                cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1,
                                              floatPivot=0,
                                              valueScale=0.0, valuePivot=0, animation="keys")
                        else:
                            cmds.selectKey(animCurveNode, addTo=True, keyframe=True, time=(keys[0], keys[-1]))
                            cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1,
                                          floatPivot=0,
                                          valueScale=0.0, valuePivot=0, animation="keys")

            cmds.bakeResults(bake_objects, time=(minTime, maxTime), **bake_results_setting)

            # set tx,tz frame value 0.
            for i in tempLocator["root"].get("skipTranslate"):
                if i == "x":
                    animCurveNode = tempLocator["root"].get('TMP').tx.source_node
                elif i == "z":
                    animCurveNode = tempLocator["root"].get('TMP').tz.source_node
                else:
                    animCurveNode = None
                if animCurveNode:
                    cmds.selectKey(clear=True)
                    cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
                    cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                                  valueScale=0.0, valuePivot=0, animation="keys")

            # for key, tmp in tempLocator.items():
            #     pointConstraint = tmp.get('pointConstraint', [])
            #     orientConstraint = tmp.get("orientConstraint", [])
            #     parentConstraint = tmp.get("parentConstraint", [])
            #     for i in [pointConstraint, orientConstraint, parentConstraint]:
            #         if i:
            #             try:
            #                 i[0].delete()
            #             except Exception as e:
            #                 # cmds.warning(e)
            #                 pass
            #     tempLocator[key]["pointConstraint"] = []
            #     tempLocator[key]["orientConstraint"] = []
            #     tempLocator[key]["parentConstraint"] = []

            # template constrain to joint
            delete_object = []
            for key, tmp in tempLocator.items():
                if key in ["ik_hand_root", "ik_foot_root", 'root']:
                    # delete_object.append(Node(key).constrain("parent", tempLocator["root"].get("TMP"), mo=True, w=True))
                    keyNode = Node(key)
                    cmds.cutKey(keyNode)
                    cns_m = TransformConstraint.create(tempLocator["root"].get("rotateTemp"),
                                                       Node(key),
                                                       constraint_attr='trs')
                    delete_object.append(cns_m)
                else:
                    # Node(key).constrain("parent", tmp.get("TMP"), mo=True, w=True)
                    keyNode = Node(key)
                    cmds.cutKey(keyNode)
                    cns_m = TransformConstraint.create(tmp.get("TMP"),
                                                       Node(key),
                                                       constraint_attr='trs')
                    delete_object.append(cns_m)
            for cns_m in delete_object:
                offsetM = cns_m.get_attr("offsetMatrix")
                offsetM[12] = offsetM[13] = offsetM[14] = 0.0
                cns_m.set_attr("offsetMatrix", offsetM)
            cmds.currentTime(0, e=True)
            rotateY = cmds.getAttr("root.rz")
            if rotate:
                if rotateY != startAngle:
                    rotateObject = tempLocator["root"].get("rotateTemp")
                    parent = rotateObject.add_parent(name='{}_group'.format(rotateObject.name), attrs='trs')
                    parent.ry.value = -rotateY

            bake_results_setting = {
                'simulation': True,
                'time': (minTime, maxTime),
                'sampleBy': 1,
                'oversamplingRate': 1,
                'disableImplicitControl': True,
                'preserveOutsideKeys': True,
                'sparseAnimCurveBake': False,
                'removeBakedAttributeFromLayer': False,
                'removeBakedAnimFromLayer': False,
                'bakeOnOverrideLayer': False,
                'minimizeRotation': True,
                'controlPoints': False,
                'shape': False
                # 'attribute': ['tx', 'ty', 'tz', 'rx', 'ry', 'rz']
            }

            if is_body_at_origin_point:
                nRoot = Node(tempLocator["root"].get("TMP"))
                if rotate:
                    body_at_origin_point(tempLocator["root"].get("rotateTemp"))
                else:
                    for attr in ["translateX", "translateY", "translateZ"]:
                        set_planKeyframe(nRoot, attr)
            if transform_to_custom_attribute:
                common.add_transform_attribute_to_custom('root')
            cmds.bakeResults(re_animation_objects, **bake_results_setting)

            cmds.delete(delete_object)
            for key, tmp in tempLocator.items():
                tmp.get("TMP").delete()

        cmds.refresh(suspend=False)
    except:
        cmds.refresh(suspend=False)
    finally:
        cmds.refresh(suspend=False)


def auto_stopping(minTime, maxTime, FOOT_L, FOOT_R, tempLocator=None, *args, **kwargs):
    """
    Auto stopping
    Args:
        minTime:  min time
        maxTime: max time
        FOOT_L: foot_l
        FOOT_R: foot_r
        tempLocator: is directory              tempLocator[joint] = {"TMP": TMP,
                                  "skipTranslate": skipTranslate,
                                  "skipOrient": skipRotate,
                                  "pointConstraint": pointConstraint,
                                  "orientConstraint": orientConstraint,
                                  "parentConstraint": [],
                                  "rotateTemp": rotateTmp,
                                  "aimObject": aimObject
                                  }
        *args:[]
        **kwargs: bake results setting

    Returns:
        root_end_time

    """
    if tempLocator is None:
        tempLocator = {}
    pos = []
    root_end_time = None
    for i in range(minTime, maxTime + 1):
        cmds.currentTime(i)
        pos.append([i, FOOT_L.get_distance_to(FOOT_R, space='world')])

    small_diff_sequences = find_small_differences(pos, threshold=0.5)

    if small_diff_sequences:
        stop = max(small_diff_sequences)
        if stop.__len__() >= 3:
            root_end_time = stop[0][0]
    if root_end_time:
        bake_Roots = [tempLocator[x].get("TMP") for x in tempLocator.keys() if
                      x in ["root", "ik_hand_root", "ik_foot_root"]]
        cmds.bakeResults(bake_Roots, time=(minTime, root_end_time), attribute=['tx', 'ty', 'tz'], **kwargs)
        cmds.currentTime(minTime)
        OpenMaya.MGlobal.displayInfo("Set root move stop time:".format(root_end_time))
    return root_end_time


def find_small_differences(seq, threshold=1.0):
    """
    find small differences in a sequence
    Args:
        seq:
        threshold:

    Returns:
        small diff sequences
    """
    small_diff_sequences = []
    current_sequence = [seq[0]]

    for i in range(1, len(seq)):
        diff = abs(seq[i][1] - seq[i - 1][1])

        if diff <= threshold:
            current_sequence.append(seq[i])
        else:
            if len(current_sequence) > 1:
                small_diff_sequences.append(current_sequence)
            current_sequence = [seq[i]]

    if len(current_sequence) > 1:
        small_diff_sequences.append(current_sequence)

    return small_diff_sequences


def transformConstraint(source, target, constraint_attr='tr', skipTranslate=None, skipRotate=None):
    """
    lsr_transformConstraint skipTranslate=['x', 'y', 'z']，skipRotate=['x', 'y', 'z']  kip offsets
    Args:
        source:
        target:
        constraint_attr:tr
        skipTranslate:['x', 'y', 'z']
        skipRotate:['x', 'y', 'z']

    Returns:

    """
    if skipTranslate is None:
        skipTranslate = []
    elif isinstance(skipTranslate, list):
        skipTranslate = skipTranslate
    elif isinstance(skipTranslate, str):
        skipTranslate = [skipTranslate]
    else:
        raise TypeError("skipTranslate must be a list or string")
    if skipRotate is None:
        skipRotate = []
    elif isinstance(skipRotate, list):
        skipRotate = skipRotate
    elif isinstance(skipRotate, str):
        skipRotate = [skipRotate]
    else:
        raise TypeError("skipRotate must be a list or string")

    cns_m = TransformConstraint.create(source, target, constraint_attr=constraint_attr)
    if skipTranslate:
        for attr in ['x', 'y', 'z']:
            if attr not in skipTranslate:
                cns_m.attr("translate{}".format(attr.upper())) >> target.attr("translate{}".format(attr.upper()))
            cns_m.translate // target.t

    if skipRotate:
        for attr in ['x', 'y', 'z']:
            if attr not in skipRotate:
                cns_m.attr("rotate{}".format(attr.upper())) >> target.attr("rotate{}".format(attr.upper()))
            cns_m.rotate // target.r
    return cns_m


def repairRootRotation(rootJoint, startAngle, endAngle, axis='y', *args, **kwargs):
    """
    repair root rotation
    Args:
        rootJoint:
        startAngle:
        endAngle:
        axis:

    Returns:

    """
    startFrame = kwargs.get("startFrame", 0)
    endFrame = kwargs.get("endFrame", 0)
    OpenMaya.MGlobal.displayInfo("rootJoint:{}, startAngle:{}, endAngle:{}, axis:{}".format(
        rootJoint, startAngle, endAngle, axis))
    animCurveNode = None
    rootJoint = Node(rootJoint)
    if axis == 'y':
        animCurveNode = rootJoint.ry.source_node
    elif axis == 'z':
        animCurveNode = rootJoint.rz.source_node
    elif axis == 'x':
        animCurveNode = rootJoint.rx.source_node
    else:
        raise ValueError("axis must be 'x', 'y', 'z'")

    if not animCurveNode:
        return

    num_keys = animCurveNode.num_keys
    # if Tpose animCurveNode.get_value(1) else animCurveNode.get_value(0)
    # get time is 0 vuale
    fStart = animCurveNode.get_keys_data()[int(startFrame)].get("value")
    OpenMaya.MGlobal.displayInfo("fStart: {}".format(fStart))
    auto_state = None
    if cmds.autoKeyframe(q=True, state=True):
        cmds.autoKeyframe(e=True, state=False)
        auto_state = True

    # set start angle
    if fStart != startAngle:
        OpenMaya.MGlobal.displayInfo("not equ!!")
        cmds.selectKey(clear=True)
        cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
        cmds.keyframe(animCurveNode, edit=True, includeUpperBound=True, relative=True, option="over",
                      valueChange=(startAngle - fStart))

    # set end angle
    cmds.selectKey(clear=True)
    fCurrentAngle = animCurveNode.get_value(num_keys - 1)
    OpenMaya.MGlobal.displayInfo("fCurrentAngle: {}".format(fCurrentAngle))

    if endAngle == 0:
        cmds.selectKey(animCurveNode, addTo=True, keyframe=True, time=(1, num_keys - 1))
        cmds.cutKey(animation="keys", clear=True)
    else:
        # fStart = animCurveNode.get_value(0)
        fStart = animCurveNode.get_keys_data()[int(startFrame)].get("value")
        fCurrentAngle = animCurveNode.get_value(num_keys - 1)
        OpenMaya.MGlobal.displayInfo("fCurrentAngle: {}".format(fCurrentAngle))
        fDifferenceAngle = abs(fStart - fCurrentAngle)
        fScale = (endAngle - fDifferenceAngle) / fDifferenceAngle + 1

        # if endAngle < 0:
        if fCurrentAngle < 0:
            fScale = fScale * -1

        OpenMaya.MGlobal.displayInfo("fScale:{}, DifferenceAngle:{}, CurrentAngle:{} ".format(fScale,
                                                                                              fDifferenceAngle,
                                                                                              fCurrentAngle))
        if fScale == 0:
            return
        cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
        cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                      valueScale=fScale,
                      valuePivot=startAngle, animation="keys")
        cmds.refresh()

        fCurrentAngle = animCurveNode.get_value(animCurveNode.num_keys - 1)
        if - 0.001 <= (fCurrentAngle + endAngle) <= 0.001:
            fDifferenceAngle = abs(fStart - fCurrentAngle)
            fScale = (endAngle - fDifferenceAngle) / fDifferenceAngle + 1
            cmds.selectKey(clear=True)
            cmds.selectKey(animCurveNode, addTo=True, keyframe=True)

            # if endAngle > 0:
            if fCurrentAngle > 0:
                cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                              valueScale=-fScale,
                              valuePivot=startAngle, animation="keys")
            # if endAngle < 0:
            if fCurrentAngle < 0:
                cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                              valueScale=fScale,
                              valuePivot=startAngle, animation="keys")

            OpenMaya.MGlobal.displayInfo("The two numbers are symmetric")

        elif -0.001 <= (fCurrentAngle + endAngle) / 2 - endAngle <= 0.001:
            OpenMaya.MGlobal.displayInfo("Both values are the same.")
        else:
            OpenMaya.MGlobal.displayInfo('The two numbers are asymmetrical and different.')
            # repairRootRotation(rootJoint, startAngle, endAngle, axis='y')
    # if startAngle != 0:
    #     rootRotationAngle(rootJoint, startAngle, *args, **kwargs)
    # cmds.selectKey(clear=True)
    # cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
    # fStart = animCurveNode.get_value(0)
    # cmds.keyframe(animCurveNode, e=True, iub=True, r=True, o="over", vc=-fStart)
    if auto_state:
        cmds.autoKeyframe(edit=True, state=True)
    OpenMaya.MGlobal.displayInfo("Repair root rotation from {} to {} angle.".format(startAngle, endAngle))


def rootRotationAngle(rotateObject, startAngle, *args, **kwargs):
    """
    repair start angle
    Args:
        rotateObject:
        startAngle:

    Returns:

    """

    parent = rotateObject.add_parent(name='{}_group'.format(rotateObject.name), attrs='trs')
    parent.ry.value = -startAngle


def find_continuous_sequences(keys):
    """
    keys = [63,64,65,66,67, 68, 69, 70, 71, 72, 73, 77, 78, 79, 80, 81, 82, 83, 84, 85]
    Find if there is a broken continuous element in the sequence keys, If there is a break,
    put the consecutive entries before the break in a list,
    and if there are consecutive entries after the break in a list, and so on
    Args:
        keys:

    Returns:
        list
    """
    keys.sort()
    result = []
    current_sequence = [keys[0]]

    for i in range(1, len(keys)):
        if keys[i] == keys[i - 1] + 1:
            current_sequence.append(keys[i])
        else:
            result.append(current_sequence)
            current_sequence = [keys[i]]
    result.append(current_sequence)
    return result


def set_planKeyframe(object, attribute):
    """
    set keyframe first key value  to  all timeline the keyframe value
    Args:
        object: Node(object)
        attribute: attribute

    Returns:

    """
    animCurveNode = object.attr(attribute).source_node
    if animCurveNode:
        cmds.select(animCurveNode)
        value = animCurveNode.get_value(0)
        cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
        cmds.keyframe(animation='keys', absolute=True, valueChange=value)


def body_at_origin_point(object):
    """
    export body at origin point
    Returns:

    """

    if cmds.objExists(object):
        root = Node(object)
        # cmds.select(root, hi=True)
        # joints = ["ik_foot_r", "ik_foot_l", 'ik_hand_gun', "pelvis"]
        joints = [x for x in root.get_children() if x.type_name == "transform"]
        # joints = cmds.ls(sl=True)
        # joints.remove(root.name)
        attribute = ["translateX", "translateY", "translateZ", "rotateX", "rotateY", "rotateZ"]
        # minTime = int(cmds.playbackOptions(query=True, minTime=True))
        # maxTime = int(cmds.playbackOptions(query=True, maxTime=True))
        # cache_anim_data = {}
        # # save cache
        # for i in range(int(minTime), int(maxTime) + 1):
        #     cmds.currentTime(i)
        #     matrices = {}
        #     for joint in joints:
        #         matrices[joint] = Node(joint).get_matrix(space='world')
        #     cache_anim_data[i] = matrices
        # cut key
        for attr in attribute:
            set_planKeyframe(root, attr)
        # update cache
        # for i in range(int(minTime), int(maxTime) + 1):
        #     cmds.currentTime(i)
        #     for joint in joints:
        #         Node(joint).set_matrix(cache_anim_data[i][joint], space='world')
        #     cmds.setKeyframe(joints, attribute=attribute)
        # cmds.currentTime(minTime)
