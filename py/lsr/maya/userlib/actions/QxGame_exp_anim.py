import os
import re
import sys

from maya import cmds
from maya import mel
from maya.OpenMaya import MFileIO

from lsr.maya.nodezoo.node import Node
from lsr.maya.userlib.actions.exp_es_anim import ES_exp_anim
import lsr.maya.animtools.fbx_exporter.export_utils as utils

import lsr.protostar.core.parameter as pa
import lsr.maya.rig.rig_global as rg
from lsr.fbxsdk.FBX_Scene import FBX_Class
from lsr.maya.animtools.export_tools import QXGame_rootMotion
from lsr.maya.animtools.export_tools import constant
from lsr.maya.animtools import fbx_api_extend
import lsr.python.core.compatible as compat


class QXGame_exp_anim(ES_exp_anim):
    """
    CGame Export Animation
    """

    @pa.bool_param(default=False)
    def export_meshes(self):
        """If True, export mesh animation."""

    @pa.bool_param(default=False)
    def is_root_motion(self):
        """If True, export root motion."""

    @pa.bool_param(default=False)
    def translate(self):
        """ If True, export root translate."""

    @pa.vector3_param(default=(1, 0, 1))
    def translateXYZ(self, *args, **kwargs):
        """ If True, export root translate XYZ."""

    @pa.bool_param(default=False)
    def rotate(self):
        """ If True, export root rotate."""

    @pa.vector3_param(default=[0, 1, 0])
    def rotateXYZ(self, *args, **kwargs):
        """ If True, export root rotate XYZ."""

    @pa.list_param(default=constant.RE_ANIMATION_OBJECTS)
    def re_animation_objects(self):
        """
        Re-export animation objects.
        Returns:

        """

    @pa.bool_param(default=True)
    def is_stop_frame(self):
        """if True, set root auto stop."""

    @pa.bool_param(default=False)
    def is_body_at_origin_point(self):
        """if True, set body at origin point."""

    @pa.float_param(default=0.0)
    def startAngle(self):
        """
        Start angle.
        Returns:

        """

    @pa.float_param(default=180.0)
    def endAngle(self):
        """
        End angle.
        Returns:

        """

    @pa.enum_param(items=('x', 'y', 'z'), default='y')
    def repairAxis(self):
        """
        Repair Axis.
        Returns:

        """

    @pa.bool_param(default=False)
    def is_repair_root_rotation(self):
        """
        if True, set root rotation .
        Returns:

        """

    @pa.int_param(default=0)
    def startFrame(self):
        """
        Repair Axis start frame
        Returns:

        """

    @pa.int_param(default=60)
    def endFrame(self):
        """
        Repair Axis end frame
        Returns:

        """

    @pa.bool_param(default=False)
    def is_repair_frame(self):
        """
        Repair Axis frame, if True repaired
        Returns:
            None
        """

    def run(self):
        super(QXGame_exp_anim, self).run()
        # Set the display mode to wireframe
        model_panels = cmds.getPanel(type='modelPanel')
        for panel in model_panels:
            cmds.modelEditor(panel, edit=True, displayAppearance='wireframe')

    def export_anim_core(self, *args, **kwargs):
        """export anim method"""
        if not self.exported_bones.value:
            cmds.warning('no root bone')
            return
        self.info("exported_bones:".format(self.exported_bones.value))
        roots = [Node(root) for root in self.exported_bones.value if cmds.objExists(root) == True]

        self.exported_roots = list(set(roots))
        # set export parameter
        utils.anim_exp_sdk_parameter()
        # mel.eval('FBXExportUpAxis z')
        cur_file_name = MFileIO.currentFile()
        base_name = os.path.basename(cur_file_name)
        dir_name = os.path.dirname(cur_file_name)

        min_time = cmds.playbackOptions(minTime=True, query=True)
        max_time = cmds.playbackOptions(maxTime=True, query=True)

        mel.eval('FBXExportBakeComplexStart -v {:d}'.format(int(min_time)))
        mel.eval('FBXExportBakeComplexEnd -v {:d}'.format(int(max_time)))
        # mel.eval('FBXExportSplitAnimationIntoTakes - c')

        saveFiles = {}

        exportNumbers = 0

        for root in self.exported_roots:
            self.info("The Export Numbers {} origin Animation.".format(exportNumbers))
            split_text = root.fn_node.partialPathName().split('|')[-1]
            if ":" in split_text:
                split_text = split_text.split(':')
                if len(split_text) >= 3:
                    prefix_name = '/'.join(split_text[:-1])
                else:
                    prefix_name = split_text[-2]
            else:
                prefix_name = split_text
            exp_joints = cmds.ls(root, dag=True, type='joint')
            exp_joints = [Node(_jnt) for _jnt in exp_joints]
            _root = None
            if ":" in exp_joints[0].name:
                _root = exp_joints[0].name.split(":")[-1]
            else:
                _root = exp_joints[0].name
            # Set all ctrls -1 frame to default pose
            rig_node = rg.RigGlobal(root)
            rig_type = rig_node.rig_type
            bone_grp = rig_node.export_bone_grp
            mesh_grp = rig_node.export_mesh_grp
            # if self.is_export_body.value and rig_type == 'body':
            #     self.info("Export Body Animation.")
            # elif self.is_export_face.value and rig_type == 'face':
            #     self.info("Export Face Animation.")
            # elif self.is_export_prop.value and rig_type == 'prop':
            #     self.info("Export Prop Animation.")
            # query control constraints , if exists, bake them, else pass
            condict = dict()
            for ctrl in rig_node.get_ctrls():
                cons = list(set([x for x in ctrl.list_connections() if
                                 cmds.objectType(x) in ['parentConstraint', 'pointConstraint', 'scaleConstraint']]))
                if cons:
                    condict[ctrl] = cons
            if condict:
                cmds.bakeSimulation(*condict.keys(), hi=True, sb=1, sm=1, t=(min_time, max_time))
                self.info("constraints to bake.")

            if rig_node.get_ctrls():
                cmds.currentTime(min_time)
                cmds.select(rig_node.get_ctrls())
                cmds.setKeyframe()

                cmds.currentTime(max_time)
                cmds.select(rig_node.get_ctrls())
                cmds.setKeyframe()

                cmds.currentTime(min_time - 1)
                cmds.playbackOptions(minTime=min_time - 1, e=True)
                rig_node.reset_pose()
                self.info('rig_node.reset_pose()')
                cmds.currentTime(min_time - 1)
            # self._root_solvers(prefix_name)
            cmds.refresh()
            # select joints
            cmds.select(exp_joints, replace=True)
            if self.export_meshes.value:
                cmds.select(mesh_grp, add=True)
                mel.eval('FBXExportShapes  -v true')
                mel.eval('FBXExportSkins -v true')
                mel.eval('FBXExportSmoothingGroups -v true')
                mel.eval('FBXExportSmoothMesh -v true')
            # raise
            cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)
            ext = re.search(r'.*(\.\D+)', base_name).group(1)

            exp_folder = '{0}/FBX_Anim/{1}'.format(dir_name, prefix_name)

            if not os.path.exists(exp_folder):
                os.makedirs(exp_folder)

            save_path = '{0}/{1}'.format(exp_folder,
                                         base_name.replace(ext, '.fbx'))
            mel.eval("FBXExportBakeComplexStart -v {}".format(min_time - 1))
            mel.eval('FBXExport -f \"{0}\" -s'.format(save_path))
            self.info("exp_file:{}".format(save_path))
            # fbx_file = FBX_Class(save_path)
            fbx_file = fbx_api_extend.FBX_SDK_Extend(save_path)
            nodes = [node.name for node in root.get_parent_hierarchy()]
            fbx_file.unparent_nodes(parent_nodes=nodes)
            non_exp_nodes = cmds.ls('*::*.{}'.format(
                "non_export"), objectsOnly=True)

            if non_exp_nodes:
                cmds.select(non_exp_nodes, deselect=True)
                fbx_file.remove_nodes_by_names(non_exp_nodes)
            fbx_file.remove_namespace()

            if not self.is_root_motion.value:
                fbx_file.batch_delete_keyframe(fbx_file.get_node_by_name(_root), -1, [1, 2, 3])
                fbx_file.set_startTime_to_zero()

            # fbx_file.save_scene_file()
            if rig_type == 'face':
                # root_N = fbx_file.get_node_by_name("root_M")
                # print("root_N:",root_N)
                # root_N.SetName("root")
                fbx_file.unparent_nodes(parent_nodes=["pelvis"])
                fbx_file.remove_nodes_by_names(["pelvis"])

            fbx_file.save_scene_file(as_ascii=False)
            # saveFiles.append(save_path)
            saveFiles[save_path] = rig_type
            exportNumbers += 1

        self.info('exportNumbers: {}'.format(exportNumbers))
        # saveFiles = list(set(saveFiles))

        if self.is_root_motion.value:
            # self.info("Start: Export Root Motion Animation.")
            for save_path, rig_type in saveFiles.items():
                if rig_type == 'body':
                    self.info("Export Root Motion Animation: {}".format(save_path))
                    translate = self.translate.value
                    translateXYZ = self.translateXYZ.value
                    rotate = self.rotate.value
                    rotateXYZ = self.rotateXYZ.value
                    re_animation_objects = self.re_animation_objects.value
                    is_root_motion = self.is_root_motion.value
                    is_stop_frame = self.is_stop_frame.value
                    is_repair_root_rotation = self.is_repair_root_rotation.value
                    startAngle = self.startAngle.value
                    endAngle = self.endAngle.value
                    repairAxis = self.repairAxis.value
                    startFrame = self.startFrame.value
                    endFrame = self.endFrame.value
                    is_repair_frame = self.is_repair_frame.value
                    is_body_at_origin_point = self.is_body_at_origin_point.value
                    kwargs = {
                        "translate": translate,
                        "translateXYZ": translateXYZ,
                        "rotate": rotate,
                        "rotateXYZ": rotateXYZ,
                        "re_animation_objects": re_animation_objects,
                        "is_root_motion": is_root_motion,
                        "is_stop_frame": is_stop_frame,
                        "is_repair_root_rotation": is_repair_root_rotation,
                        "startAngle": startAngle,
                        "endAngle": endAngle,
                        "repairAxis": repairAxis,
                        "startFrame": startFrame,
                        "endFrame": endFrame,
                        "is_repair_frame": is_repair_frame,
                        "is_body_at_origin_point": is_body_at_origin_point
                    }
                    args = []

                    cmds.file(save_path, open=True, force=True)
                    cmds.currentTime(min_time - 1)
                    QXGame_rootMotion.create_root_motion(*args, **kwargs)

                    # Cut key -1 frame
                    cmds.select(cmds.ls(type=['animCurveTL', 'animCurveTA', 'animCurveTU', 'animCurveTT']))
                    try:
                        cmds.cutKey(time=(-1, -1))
                    except:
                        pass

                    # if self.is_body_at_origin_point.value:
                    #     self.body_at_origin_point()

                    # Set root rotate to zero, if skip stranslateY, keep the values
                    skipTY = 0
                    if translateXYZ[1] == 1:
                        skipTY = 1

                    self.set_index0_frame_to_zero('root', skipTY, min_time)

                    cmds.playbackOptions(minTime=min_time, edit=True)
                    cmds.playbackOptions(animationStartTime=min_time, edit=True)
                    self.delete_namespace()

                    utils.anim_exp_sdk_parameter()
                    if self.export_meshes.value:
                        mel.eval('FBXExportShapes  -v true')
                        mel.eval('FBXExportSkins -v true')
                        mel.eval('FBXExportSmoothingGroups -v true')
                        mel.eval('FBXExportSmoothMesh -v true')
                    export = [x for x in cmds.ls(assemblies=True) if x not in ['persp', 'top', 'front', 'side']]
                    cmds.select(export, replace=True)
                    cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)
                    mel.eval("FBXExportBakeComplexStart -v {}".format(min_time))
                    rootMotion = save_path.replace('.fbx', '_rootMotion.fbx')
                    mel.eval('FBXExport -f \"{0}\" -s'.format(rootMotion))

                    try:
                        os.remove(save_path)
                        os.rename(rootMotion, save_path)
                    except Exception as e:
                        self.error(e)

                    self.info("export {}".format(save_path))

        cmds.file(new=True, force=True)

    def set_planKeyframe(self, object, attribute):
        """
        set keyframe first key value  to  all timeline the keyframe value
        Args:
            object: Node(object)
            attribute: attribute

        Returns:

        """
        animCurveNode = object.attr(attribute).source_node
        if animCurveNode:
            cmds.select(animCurveNode)
            value = animCurveNode.get_value(0)
            cmds.selectKey(animCurveNode, addTo=True, keyframe=True)
            cmds.keyframe(animation='keys', absolute=True, valueChange=value)

    def body_at_origin_point(self):
        """
        export body at origin point
        Returns:

        """

        if cmds.objExists("root"):
            root = Node("root")
            # cmds.select(root, hi=True)
            # joints = ["ik_foot_r", "ik_foot_l", 'ik_hand_gun', "pelvis"]
            joints = [x for x in root.get_children() if x.type_name == "joint"]
            # joints = cmds.ls(sl=True)
            # joints.remove(root.name)
            attribute = ["translateX", "translateY", "translateZ", "rotateX", "rotateY", "rotateZ"]
            minTime = int(cmds.playbackOptions(query=True, minTime=True))
            maxTime = int(cmds.playbackOptions(query=True, maxTime=True))
            cache_anim_data = {}
            # save cache
            for i in range(int(minTime), int(maxTime) + 1):
                cmds.currentTime(i)
                matrices = {}
                for joint in joints:
                    matrices[joint] = Node(joint).get_matrix(space='world')
                cache_anim_data[i] = matrices
            # cut key
            for attr in attribute:
                self.set_planKeyframe(root, attr)
            # update cache
            for i in range(int(minTime), int(maxTime) + 1):
                cmds.currentTime(i)
                for joint in joints:
                    Node(joint).set_matrix(cache_anim_data[i][joint], space='world')
                cmds.setKeyframe(joints, attribute=attribute)
            cmds.currentTime(minTime)

    def set_index0_frame_to_zero(self, root, skipTY, startTime=0):
        """
        Set root joint frame 0 to 0, if frame 0 value not zero, set to zero
        Returns:

        """

        if cmds.keyframe(root, query=True, valueChange=1, time=(startTime - 1, startTime)):
            if any([x for x in cmds.keyframe(root, query=True, valueChange=1, time=(startTime - 1, startTime)) if
                    float(x) != 0.0]):
                # method 1
                cmds.select(root)
                animLayer = mel.eval('layerEditorCreateAnimLayer( true, false);')
                mel.eval('updateAddButtons();')
                root = Node("root")
                root.set_attr("translateX", 0)
                if not skipTY:
                    # if not skip TranslateY set it value 0
                    root.set_attr("translateY", 0)
                root.set_attr("translateZ", 0)
                # root.set_attr("rotateX", 0)
                # root.set_attr("rotateY", 0)
                # root.set_attr("rotateZ", 0)
                cmds.setKeyframe(root, breakdown=False, preserveCurveShape=False, hierarchy=False)
                cmds.playbackOptions(edit=True, minTime=startTime)
                base_animation = cmds.animLayer(animLayer, query=1, parent=1)
                mel.eval('animLayerMerge({"%s", "%s"})' % (animLayer, base_animation))
                cmds.delete(cmds.ls(type='animLayer'))
                animCurveNode = root.v.source_node
                if animCurveNode:
                    animCurveNode.clear_keys()
                root.v.value = 1
                cmds.currentTime(startTime)

    def delete_namespace(self):
        """
        Delete namespace
        Returns:

        """
        names = [name for name in cmds.namespaceInfo(listOnlyNamespaces=True, fullName=True) if
                 name not in [u'UI', u'shared']]
        for name in names:
            cmds.namespace(removeNamespace=name, mergeNamespaceWithRoot=True)

    @staticmethod
    def set_keyframeValue_than_0_to_zero(node, attr):
        """
        Set negative values less than 0 to 0
        Args:
            node:
            attr:

        Returns:

        """
        animCurveNode = node.attr(attr).source_node
        # animCurveNode = node.ty.source_node
        if animCurveNode:
            cmds.select(animCurveNode)
            cmds.selectKey(clear=True)
            # find time range -1 < 0
            keys = []
            for key, value in animCurveNode.get_keys_data().items():
                if value['value'] < 0:
                    keys.append(int(key))
            if keys:
                cmds.selectKey(animCurveNode, addTo=True, keyframe=True, time=(keys[0], keys[-1]))
                cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                              valueScale=0.0, valuePivot=0, animation="keys")

    def _root_solvers(self, namespace):
        """
        Recoding position
        Args:
            namespace:

        Returns:

        """
        if cmds.objExists("{}:pelvis".format(namespace)):
            pelvis_loc = Node.create("transform", name="{}:pelvis_loc".format(namespace))
            con1 = pelvis_loc.constrain("point", "{}:pelvis".format(namespace), maintainOffset=False,
                                        weight=True)
            root_loc = Node.create("transform", name="{}:root_loc".format(namespace))
            root_loc.constrain("point", pelvis_loc, maintainOffset=False, skip=['y'],
                               weight=True).delete()
            con2 = root_loc.constrain("point", pelvis_loc, maintainOffset=True,
                                      weight=True)

            # pelvis_loc.set_parent("{}:root".format(namespace))
            minTime = int(cmds.playbackOptions(query=True, minTime=True))
            maxTime = int(cmds.playbackOptions(query=True, maxTime=True))
            # bake template locator
            cmds.bakeResults([pelvis_loc, root_loc],
                             time=(minTime, maxTime),
                             oversamplingRate=1,
                             disableImplicitControl=True,
                             preserveOutsideKeys=True,
                             sparseAnimCurveBake=False,
                             removeBakedAttributeFromLayer=False,
                             removeBakedAnimFromLayer=False,
                             bakeOnOverrideLayer=False,
                             minimizeRotation=True,
                             controlPoints=False,
                             shape=False)
            cmds.delete(con1, con2)
            # set -1 < 0 value
            animCurveNode = root_loc.ty.source_node
            if animCurveNode:
                cmds.selectKey(clear=True)
                # find time range -1 < 0
                keys = []
                for key, value in animCurveNode.get_keys_data().items():
                    if value['value'] < 0:
                        keys.append(int(key))
                if keys:
                    cmds.selectKey(animCurveNode, addTo=True, keyframe=True, time=(keys[0], keys[-1]))
                    cmds.scaleKey(includeUpperBound=False, timeScale=True, timePivot=0, floatScale=1, floatPivot=0,
                                  valueScale=0.0, valuePivot=0, animation="keys")

            ctrl1 = Node("{}:root_03_M_CTRL".format(namespace))
            ctrl2 = Node("{}:IkHandRoot_M_CTRL".format(namespace))
            ctrl3 = Node("{}:IkFoot_M_CTRL".format(namespace))
            constrain_list = []
            for ctrl in [ctrl1, ctrl2, ctrl3]:
                constrain_list.append(ctrl.constrain("point", root_loc, maintainOffset=False,
                                                     weight=True))
            cmds.bakeResults([ctrl1, ctrl2, ctrl3],
                             time=(minTime, maxTime),
                             oversamplingRate=1,
                             disableImplicitControl=True,
                             preserveOutsideKeys=True,
                             sparseAnimCurveBake=False,
                             removeBakedAttributeFromLayer=False,
                             removeBakedAnimFromLayer=False,
                             bakeOnOverrideLayer=False,
                             minimizeRotation=True,
                             controlPoints=False,
                             attribute=['tx', 'ty', 'tz'],
                             shape=False)
            cmds.delete(constrain_list)
            # if self.is_body_at_origin_point.value:
            #     attribute = ["translateX", "translateY", "translateZ", "rotateX", "rotateY", "rotateZ"]
            #     for ctrl in [ctrl1, ctrl2, ctrl3]:
            #         for attr in attribute:
            #             self.set_planKeyframe(ctrl, attr)
