# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2024-12-02 14:24:57
# @Last Modified by:   <PERSON>
# @Last Modified time: 2025-01-24 12:52:17
import maya.cmds as cmds
import maya.mel as mel
import os
import shutil
import json

USERLAYOUTNAME = 'userLayout'
CAPTURECAMERANAME = 'captureCamera'
CAMERANMAES = ['front', 'back', 'right', 'left']
CAMERANROTATE = [0, 180, 270, 90]
CAMERAALLGRP = f'CameraAll_Grp'
TMP_ROTATE = 'tmp_rotate'
LOCKATTRS = ["translateX",
            "translateY",
            "translateZ",
            "rotateX",
            "rotateY",
            "rotateZ"]


def load_camera_cofing():
    return {'info' : ['pelvis', 'head', 500]}


def find_pelvis_jot(name_space=''):
    pelvis_names = [
        'pelvis',
        'Pelvis',
        'spine',
        'Spine',
        'body',
        'Body',
        'root',
        'Root'
    ]
    pelvis_jot = ''
    for pelvis_name in pelvis_names:
        nodes = cmds.ls('%s::*%s*' % (name_space, pelvis_name), type=('transform', 'joint'))
        if nodes:
            pelvis_jot = nodes[0]
            break
    return pelvis_jot


def find_neck_jot(name_space=''):
    neck_names = [
        'head',
        'Head',
        'neck',
        'Neck'
    ]
    neck_jot = ''
    for neck_name in neck_names:
        nodes = cmds.ls('%s::*%s*' % (name_space, neck_name), type=('transform', 'joint'))
        if nodes:
            neck_jot = nodes[0]
            break
    return neck_jot


def find_pelvis_joint(name_space=''):
    # info = load_camera_cofing()
    # jnt1, jnt2 = info['info'][0], info['info'][1]
    # pelvis_jnt = cmds.ls(f'*:{jnt1}')
    # if pelvis_jnt:
    #     name_space = pelvis_jnt[0].split(':')[0]
    # else:
    #     name_space = None
    #
    # if name_space:
    #     pelvis_jnt = f'{name_space}:{jnt1}'
    #     neck_jnt = f'{name_space}:{jnt2}'
    # else:
    #     pelvis_jnt = jnt1
    #     neck_jnt = jnt2
    pelvis_jnt = find_pelvis_jot(name_space)
    neck_jnt = find_neck_jot(name_space)
    return [pelvis_jnt, neck_jnt]


def create_camera(offset=None):
    up_axis = cmds.upAxis(q=True, ax=True)
    info = load_camera_cofing()
    if offset is None:
        offset = info['info'][2]
    if cmds.objExists(CAMERAALLGRP):
        childs = [f'{k}_camera' for k in CAMERANMAES]
        cmds.delete(CAMERAALLGRP, childs)
        
    camera_all_Grp = cmds.group(em=1, name=CAMERAALLGRP)
    for k,v in zip(CAMERANMAES, CAMERANROTATE):
        camera = cmds.camera()
        camera_name = cmds.rename(camera[0],f'{k}_camera')
        cmds.setAttr(f'{camera_name}.nearClipPlane', 10)
        camera_offset = cmds.group(em=1, name=f'{camera_name}_offset')
        camera_grp = cmds.group(em=1, name=f'{camera_name}_grp')
        cmds.parent(camera_name, camera_offset)
        cmds.parent(camera_offset, camera_grp)
        cmds.parent(camera_grp, camera_all_Grp)
        cmds.setAttr(f'{camera_offset}.tz', offset)
        cmds.setAttr(f'{camera_offset}.selectHandleZ', offset)
        cmds.setAttr(f'{CAMERAALLGRP}.selectHandleZ', offset)
        if up_axis == 'z':
            # v = v + 180
            cmds.setAttr(f'{camera_grp}.rx', 90)
            cmds.setAttr(f'{camera_grp}.rz', v)
        else:
            cmds.setAttr(f'{camera_grp}.ry', v)
        _ = [cmds.setAttr(f'{camera_name}.{attr}',lock=1) for attr in LOCKATTRS]
    return camera_all_Grp


def get_up_axis_info():
    """
    Get the up axis info.
    Returns:
        list: [point_skip, orient_skip, ro_attr]
    """
    up_axis = cmds.upAxis(q=True, ax=True)
    if up_axis == 'z':
        point_skip = 'z'
        orient_skip = ['x', 'y']
        ro_attr = 'rz'
    else:
        point_skip = 'y'
        orient_skip = ['x', 'z']
        ro_attr = 'ry'
    return [point_skip, orient_skip, ro_attr]


def create_follow_camera(current_file, offset=None, rotate=None, name_space=''):
    # create_follow_camera
    file_name = os.path.splitext(os.path.basename(current_file))[0]
    result = file_name.split('_') 

    if result[-1].isdigit() and result[-2].isdigit():
        star_frame = result[-1] 
        end_frame = result[-2]
    else:
        star_frame = cmds.playbackOptions(minTime=True, query=True)
        end_frame = cmds.playbackOptions(maxTime=True, query=True)

    # point_skip = 'y'
    # orient_skip = ['x', 'z']
    # ro_attr = 'ry'
    # up_axis = cmds.upAxis(q=True, ax=True)
    # if up_axis == 'z':
    #     point_skip = 'z'
    #     orient_skip = ['x', 'y']
    #     ro_attr = 'rz'
    point_skip, orient_skip, ro_attr = get_up_axis_info()
    camera_all_Grp = create_camera(offset)
    if not cmds.objExists(f'{camera_all_Grp}.{TMP_ROTATE}'):
        cmds.addAttr(camera_all_Grp, ln=TMP_ROTATE, at='double', dv=0)
    pelvis_neck_joint = find_pelvis_joint(name_space)
    cmds.setAttr(f'{camera_all_Grp}.rotateOrder', 2)
    cmds.matchTransform(camera_all_Grp, pelvis_neck_joint[0], pos=1)
    con_node = cmds.pointConstraint(pelvis_neck_joint[0], camera_all_Grp, skip=point_skip)
    orient_con_node = cmds.orientConstraint(pelvis_neck_joint[0], pelvis_neck_joint[1], camera_all_Grp, skip=orient_skip)
    ro_y = cmds.getAttr(f'{camera_all_Grp}.{ro_attr}')
    cmds.setAttr(f"{camera_all_Grp}.selectHandleY", ro_y)
    cmds.setAttr(f'{camera_all_Grp}.{TMP_ROTATE}', ro_y)
    cmds.delete(orient_con_node)
    bake_anim_keys(node=[f'{camera_all_Grp}.t'], min_time=star_frame, max_time=end_frame)
    
    cmds.filterCurve(f'{camera_all_Grp}.t', f='simplify', timeTolerance=0.05)
    cmds.filterCurve(f'{camera_all_Grp}.t', f='keyReducer', precisionMode=0, precision=10)
    cmds.keyTangent(f'{camera_all_Grp}.t', edit=True, itt='auto', ott='auto', animation='objects')
    cmds.delete(con_node)

    if rotate is not None:
        ro_y = rotate
        cmds.setAttr(f'{camera_all_Grp}.{ro_attr}', ro_y)

    info = load_camera_cofing()
    if offset is None:
        offset = info['info'][2]
    return [offset, ro_y]


def camera_info_path():
    documents_path = cmds.internalVar(userPrefDir=True)
    maya_documents_path = os.path.join(documents_path, f"pane_camera_info.json")
    maya_documents_path = maya_documents_path.replace('\\', '/')
    return maya_documents_path


def bake_anim_keys(node, min_time, max_time, *args):
    """

    Args:
        node (str or list): nodes need to be baked
        min_time (int or float): min time value for bake
        max_time (int or float): max time value for bake

    Returns:
        None
    """
    cmds.refresh(suspend=True)
    cmds.bakeResults(node, t=(int(min_time), int(max_time)), simulation=True, hierarchy='none',
                     sampleBy=5, disableImplicitControl=True, preserveOutsideKeys=True,
                     sparseAnimCurveBake=False, removeBakedAttributeFromLayer=False,
                     bakeOnOverrideLayer=False, minimizeRotation=True,
                     controlPoints=False, shape=False)
    cmds.refresh(suspend=False)


def copy_config_to_documents():
    # copy 配置好的ui设置 到文档下的workspace 
    capture_camera_json = f'{CAPTURECAMERANAME}.json'
    current_file_path = os.path.dirname(os.path.abspath(__file__))
    user_layout_json= os.path.join(current_file_path, capture_camera_json)

    user_Pref_Dir = cmds.internalVar(userPrefDir=True)
    maya_documents_path = os.path.join(user_Pref_Dir, f"workspaces")
    maya_documents_json = os.path.join(maya_documents_path, capture_camera_json)
    if os.path.isfile(maya_documents_json):
        os.remove(maya_documents_json)
    shutil.copy(user_layout_json, maya_documents_path)


def hide_ui():
    point_skip, orient_skip, ro_attr = get_up_axis_info()
    save_user_layout_pane_camera()
    if cmds.objExists(CAMERAALLGRP): 
        ro_y = cmds.getAttr(f'{CAMERAALLGRP}.selectHandleY')
        cmds.setAttr(f'{CAMERAALLGRP}.{ro_attr}', ro_y)

    # current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
    # if current_workspace == CAPTURECAMERANAME:
    #     return
    # copy_config_to_documents()
    # # 保存当前用户的布局
    # current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
    # if not current_workspace == CAPTURECAMERANAME:
    #     cmds.workspaceLayoutManager(saveAs=USERLAYOUTNAME)
    #
    # cmds.workspaceLayoutManager(setCurrent=CAPTURECAMERANAME)
    #
    # set_pane_camera()
    # # 隐藏ui上面的两条menu bar
    # switch_panes_menubar(0)
    # cmds.iconTextCheckBox('white_model', e=1, value=1)
    # for i in cmds.getPanel( type='modelPanel'):
    #         cmds.modelEditor(i, edit=True,  displayAppearance="smoothShaded", useDefaultMaterial=0)


def hide_45_ui():
    point_skip, orient_skip, ro_attr = get_up_axis_info()
    hide_ui()
    ro_y = cmds.getAttr(f'{CAMERAALLGRP}.{ro_attr}')
    cmds.setAttr(f'{CAMERAALLGRP}.{ro_attr}', ro_y+45)


def hide_neg45_ui():
    point_skip, orient_skip, ro_attr = get_up_axis_info()
    hide_ui()
    ro_y = cmds.getAttr(f'{CAMERAALLGRP}.{ro_attr}')
    cmds.setAttr(f'{CAMERAALLGRP}.{ro_attr}', ro_y-45)


def set_user_workspace_layout():
    current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
    if current_workspace == USERLAYOUTNAME:
        return
    switch_panes_menubar(1)
    cmds.workspaceLayoutManager(setCurrent=USERLAYOUTNAME)
    mel.eval('toggleMenuBarsInPanels true;')
    # 设置用户初始pane 相机设置
    pane_camera_info_path = camera_info_path()
    with open(pane_camera_info_path, 'r') as f:
        pane_camera_info = json.load(f)
    for pane, camera in pane_camera_info.items():
        f1 = cmds.modelPanel(pane, q=True, barLayout=True)
        if f1:
            try:
                cmds.lookThru(pane, camera)
            except:
                pass
            cmds.modelEditor(pane, e=1, allObjects=1)
            cmds.modelEditor(pane, e=1, polymeshes=1)
            cmds.modelEditor(pane, e=1, locators=1)
            cmds.modelEditor(pane, e=1, controllers=1)

def show_ui():
    current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
    if current_workspace == USERLAYOUTNAME:
        # current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
        # if current_workspace == CAPTURECAMERANAME:
        #     return
        copy_config_to_documents()
        # 保存当前用户的布局
        # current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
        # if not current_workspace == CAPTURECAMERANAME:
        #     cmds.workspaceLayoutManager(saveAs=USERLAYOUTNAME)

        cmds.workspaceLayoutManager(setCurrent=CAPTURECAMERANAME)

        set_pane_camera()
        # 隐藏ui上面的两条menu bar
        switch_panes_menubar(0)
        cmds.iconTextCheckBox('white_model', e=1, value=1)
        for i in cmds.getPanel(type='modelPanel'):
            cmds.modelEditor(i, edit=True, displayAppearance="smoothShaded", useDefaultMaterial=0)

    else:
        set_user_workspace_layout()


def playblase_setUI(is_user_pane=False, user_camera_list=None):
    current_workspace = cmds.workspaceLayoutManager(q=1, current=1)
    if not current_workspace == CAPTURECAMERANAME:
        return
    mel.eval('source "setNamedPanelLayout.mel";')
    mel.eval('setNamedPanelLayout "Four View";')
    set_pane_camera_for_playblase(is_user_pane, user_camera_list)


def set_pane_camera():
    camera_list = [f'{k}_camera' for k in CAMERANMAES]
    for num,cam in enumerate(camera_list):
        if cmds.objExists(cam):
            pane_num = num + 1
            # print(pane_num, cam)
            cmds.lookThru(f'modelPanel{pane_num}', cam)


def set_pane_camera_for_playblase(is_user_pane=False, user_camera_list=None):
    camera_list = [f'{k}_camera' for k in CAMERANMAES]
    panes = get_panes()
    if is_user_pane:
        if user_camera_list:
            camera_list = user_camera_list
        else:
            camera_list = get_camera_list_by_panes(panes)
    for pane_num, cam in zip(panes, camera_list):
        if cmds.objExists(cam):
            cmds.modelEditor(pane_num, e=1, allObjects=0)
            cmds.modelEditor(pane_num, e=1, polymeshes=1)
            cmds.lookThru(pane_num, cam)


def get_panes():
    currPane1 = cmds.paneLayout('viewPanes', query=True, pane1=True)
    currPane2 = cmds.paneLayout('viewPanes', query=True, pane2=True)
    currPane3 = cmds.paneLayout('viewPanes', query=True, pane3=True)
    currPane4 = cmds.paneLayout('viewPanes', query=True, pane4=True)
    return [currPane1, currPane2, currPane3, currPane4]

def get_camera_list_by_panes(panes=None):
    if panes is None:
        panes = get_panes()
    camera_list = []
    for pane in panes:
        camera = cmds.modelEditor(pane, query=True, camera=True)
        camera_list.append(camera)
    return camera_list


def switch_panes_menubar(switch):
    not_switch = not switch
    cmds.window('MayaWindow', e=1, mbv=switch)
    for pane in  cmds.getPanel( type='modelPanel'):
        cmds.panel(pane, e=1, mbv=switch) 
        f1 = cmds.modelPanel(pane, q=True, barLayout=True)
        if f1:
            cmds.frameLayout(f1, e=1, collapse=not_switch)
            cmds.modelEditor(pane, e=1, allObjects=switch) 
            cmds.modelEditor(pane, e=1, polymeshes=not_switch) 


def save_user_layout_pane_camera():
    pane_camera_info = {}
    for pane in cmds.getPanel( type='modelPanel'):
        camera_name = cmds.modelEditor(pane, query=True, camera=True)
        pane_camera_info[pane] = camera_name
    maya_documents_path = camera_info_path()
    with open(maya_documents_path, 'w') as f:
        json.dump(pane_camera_info, f)


def create_other_dir(dirname):
    '''创建文件夹
    '''
    if not os.path.exists(dirname): os.makedirs(dirname)
    return dirname


def open_file_add_camera(
        file,
        save_as_switch=True,
        camera_switch=False,
        playblase_switch=False,
        white_model_q=False,
        wire_model_q=False,
        Material_model_q=False,
        offset=None,
        rotate=None,
        *args, **kwargs
    ):
    try:
        cmds.file(file, open=1, force=True)
    except:
        pass
    
    chenge_iconbox(white_model_q,wire_model_q,Material_model_q)
    if camera_switch:
        create_follow_camera(file, offset, rotate)
        playblase_setUI()
        cmds.refresh()

    if playblase_switch:
        cmds.refresh()
        cmds.dotnetAutoViewportRecoderUI()

    if save_as_switch:
        dir_name = os.path.dirname(file)
        basename = os.path.basename(file)
        dirname = create_other_dir(os.path.join(dir_name,'CaptureCamera'))
        save_path = os.path.join(dirname, basename)
    else:
        save_path = file
    ext = 'mayaAscii' if os.path.splitext(save_path)[-1].lower() == '.ma' else 'mayaBinary'
    cmds.file(rename=save_path)
    cmds.file(save=True, type=ext)


def chenge_iconbox(white_model_q,wire_model_q,Material_model_q):
    """
    设置渲染模式
    """
    if white_model_q:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True, displayAppearance="smoothShaded", useDefaultMaterial=0)
    else:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True, displayAppearance="wireframe")
            
    if wire_model_q:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True, displayAppearance="smoothShaded", wireframeOnShaded=1, useDefaultMaterial=0)
    else:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True, wireframeOnShaded=0)

    if Material_model_q:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True,  displayAppearance="smoothShaded", displayTextures=1, useDefaultMaterial=0)
    else:
        for i in cmds.getPanel( type='modelPanel'):
            cmds.modelEditor(i, edit=True, displayTextures=0)