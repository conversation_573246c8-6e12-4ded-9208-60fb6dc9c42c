# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2024-12-02 16:37:30
# @Last Modified by:   <PERSON>
# @Last Modified time: 2025-02-06 10:49:45
import os
import io
import inspect
import traceback
import threading
from functools import partial
import maya.cmds as cmds
import maya.api.OpenMaya as om
from lsr.qt.core import QtWidgets, QtCore, QtGui
from lsr.maya.cgame import MWidgets
from lsr.maya.cgame.capture_camera import main
import lsr.maya.startup.action as action
import maya.OpenMayaUI as omui
from shiboken2 import wrapInstance
from lsr.python.core.compatible import long
import lsr.maya.animtools.retargeter.widgets as wd
import maya
# import imp
# imp.reload(MWidgets)
# imp.reload(main)

UINAME = 'CaptureCamera'
def get_version():
    with io.open(inspect.getfile(inspect.currentframe()), "r", encoding="utf-8") as f:
        lines = f.readlines()
        line = lines[5 - 1].strip()
        line =  line.split(' ')[-2:]
    return '%s - %s' % (line[0], line[1])


def get_maya_control(control_name):
    ptr = omui.MQtUtil.findControl(control_name)
    if ptr is not None:
        return wrapInstance(long(ptr), QtWidgets.QWidget)
    else:
        return None
    

class baseUI(MWidgets.widgets):
    # global_stop_flag = False
    
    def __init__(self):
        self.folder_icon = action.resolve_icon_path('folder.png').replace('\\', '/')
        self.file_icon = action.resolve_icon_path('feedback.png').replace('\\', '/')
        self.success_icon = action.resolve_icon_path('accept1.png').replace('\\', '/')
        self.failed_icon = action.resolve_icon_path('decline.png').replace('\\', '/')
        self.camera_icon = action.resolve_icon_path('camera_1.png').replace('\\', '/')
        self.famera_icon = action.resolve_icon_path('camera_0.png').replace('\\', '/')
        self.camera_on_icon = action.resolve_icon_path('captureCamera.png').replace('\\', '/')
        self.camera_off_icon = action.resolve_icon_path('captureCamera_off.png').replace('\\', '/')
        self.rec_on_icon = action.resolve_icon_path('rec_on.png').replace('\\', '/')
        self.rec_off_icon = action.resolve_icon_path('rec_off.png').replace('\\', '/')
        self.save_icon = action.resolve_icon_path('camera_save.png').replace('\\', '/')
        self.saveas_icon = action.resolve_icon_path('camera_saveAs.png').replace('\\', '/')
        self.camera_distance = 500
        self.camera_offsets = [f'{k}_camera_offset' for k in main.CAMERANMAES]
        cmds.workspaceLayoutManager(saveAs=main.USERLAYOUTNAME)
        cmds.playbackOptions(v='all')
        super(baseUI, self).__init__()
        self.ani_size = (850, 460)
        # setupUI
        self.createBaseWidget()
        self.setup_ui()
        self.connect_signals()
        self.refresh_slider_value()
        self.set_namespace()

    def createBaseWidget(self):
        self.setObjectName(UINAME)
        self.setWindowTitle( '%s V %s' % (UINAME, get_version()) )
        self.resize(850, 460)
        self.setWindowFlags(self.windowFlags() ^ QtCore.Qt.WindowContextHelpButtonHint)
        self.base_widget = QtWidgets.QWidget(self)
        self.base_widget.setObjectName("base_widget")
        self.setCentralWidget(self.base_widget)
        self.setCentralWidget(self.base_widget)

    def setup_ui(self):
        # 创建菜单栏menubar
        # self.menubar = QtWidgets.QMenuBar(self)
        # self.menubar.setGeometry(QtCore.QRect(0, 0, 529, 40))
        # self.setMenuBar(self.menubar)

        # self.menuFile = QtWidgets.QMenu(self.menubar)
        # self.menuFile.setTitle("File Config")
        
        # self.actionLoad = QtWidgets.QAction(self)
        # self.actionExport = QtWidgets.QAction(self)
        # self.actionLoad.setText("Load")
        # self.actionExport.setText("Export")
        # self.menuFile.addAction(self.actionLoad)
        # self.menuFile.addAction(self.actionExport)

        # self.menubar.addAction(self.menuFile.menuAction())


        config_grid = QtWidgets.QGridLayout()
        pix = QtGui.QPixmap(self.folder_icon)
        self.pg_config = wd.ConfigFileWidgetGroup(
            pix, 'Config File', config_grid, 2,
            placeholder='Project config file path.')
        




        self.checkbox_layout = QtWidgets.QHBoxLayout()
        ########iconTextCheckBox########
        self.window = cmds.window()
        cmds.columnLayout( adjustableColumn=True )
        Wire_frame = cmds.iconTextCheckBox('Wire_frame', style='iconOnly', image1='WireFrame.png', 
                                           changeCommand = lambda i: self.chenge_iconbox()
                                           ) 
        
        white_model = cmds.iconTextCheckBox('white_model', style='iconOnly', image2='Shaded.png', 
                                           changeCommand = lambda i: self.chenge_iconbox()
                                           ) 
        
        wire_model = cmds.iconTextCheckBox('wire_model', style='iconOnly', image1='WireFrameOnShaded.png', 
                                           changeCommand = lambda i: self.chenge_iconbox()
                                           ) 
        
        Material_model = cmds.iconTextCheckBox('Material_model', style='iconOnly', image3='Textured.png', 
                                           changeCommand = lambda i: self.chenge_iconbox()
                                           ) 
        self.Wire_frame = get_maya_control(Wire_frame)
        self.white_model = get_maya_control(white_model)
        self.wire_model = get_maya_control(wire_model)
        self.Material_model = get_maya_control(Material_model)
        # spacer
        spacer = QtWidgets.QSpacerItem(120, 5, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.checkbox_layout.addWidget(self.white_model)
        self.checkbox_layout.addWidget(self.wire_model)
        self.checkbox_layout.addWidget(self.Material_model)
        self.checkbox_layout.addItem(spacer)
        
        self.tab_Widget = QtWidgets.QTabWidget(self.base_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHeightForWidth(self.tab_Widget.sizePolicy().hasHeightForWidth())
        self.tab_Widget.setSizePolicy(sizePolicy)
        self.tab_Widget.setStyleSheet("font: 9pt \"微软雅黑\";")
        # self.tab_Widget.setTabVisible(1,0)


        # 处理FBX文件 Tab
        self.file_lise_tap = QtWidgets.QWidget()
        self.tab_Widget.addTab(self.file_lise_tap, u"     FileList    ")

        # 错误提示 Tab
        self.info_tap = QtWidgets.QWidget()
        self.info_tap.setObjectName("Error_Message")
        self.infoMainLayout = QtWidgets.QVBoxLayout(self.info_tap)

        # 创建main grid layout 放置tab widget
        self.main_grid_layout = QtWidgets.QGridLayout(self.base_widget)
        self.main_grid_layout.addLayout(self.checkbox_layout, 0, 1, 1, 1)
        self.main_grid_layout.addWidget(self.tab_Widget, 1, 0, 1, 4)
        self.main_grid_layout.setRowStretch(1, 1)
        
        self.mainLayout = QtWidgets.QVBoxLayout(self.file_lise_tap)
        ###############################################################
        self.parameter_layout = QtWidgets.QGridLayout()
        self.save_other_checkBox = self.checkBox('Save Scene As', self.save_icon, self.saveas_icon)
        self.save_other_checkBox.setObjectName("save_checkBox")
        self.save_other_checkBox.setChecked(True)
        ###############################################################
        self.cam_checkBox_layout = QtWidgets.QGridLayout()
        self.f_cam_checkBox = self.checkBox('F', self.camera_icon, self.famera_icon)
        self.b_cam_checkBox = self.checkBox('B', self.camera_icon, self.famera_icon)
        self.l_cam_checkBox = self.checkBox('L', self.camera_icon, self.famera_icon)
        self.r_cam_checkBox = self.checkBox('R', self.camera_icon, self.famera_icon)
        self.cam_checkBox_layout.addWidget(self.f_cam_checkBox, 0, 0, 1, 1)
        self.cam_checkBox_layout.addWidget(self.b_cam_checkBox, 0, 1, 1, 1)
        self.cam_checkBox_layout.addWidget(self.l_cam_checkBox, 1, 0, 1, 1)
        self.cam_checkBox_layout.addWidget(self.r_cam_checkBox, 1, 1, 1, 1)
        ###############################################################
        # Create a label to display the slider value
        self.value_layout = QtWidgets.QGridLayout()
        self.lenght_label = QtWidgets.QLabel("Distance")
        self.lenght_slider = MWidgets.QDoubleSpinBox()
        self.rotate_label = QtWidgets.QLabel("Rotate")
        self.rotate_slider = MWidgets.QDoubleSpinBox()
        self.value_layout.addWidget(self.lenght_label, 0, 0, 1, 1)
        self.value_layout.addWidget(self.lenght_slider,0, 1, 1, 1)
        self.value_layout.addWidget(self.rotate_label, 1, 0, 1, 1)
        self.value_layout.addWidget(self.rotate_slider,1, 1, 1, 1)
        ###############################################################
        self.parameter_layout.addWidget(self.save_other_checkBox, 0, 0, 1, 1)
        self.parameter_layout.addLayout(self.cam_checkBox_layout, 0, 1, 1, 1)
        self.parameter_layout.addLayout(self.value_layout,        0, 2, 1, 1)
        ###############################################################

        # 创建 ListWidget
        add_files_action = QtWidgets.QAction("add files", self)
        clear_all_action = QtWidgets.QAction("delete all", self)
        self.fileListView = MWidgets.MListWidget(parent=self, actions=[add_files_action, clear_all_action])
        # 设置右键菜单
        self.fileListView.actions[0].triggered.connect(self.getFilePath)
        self.fileListView.actions[1].triggered.connect(self.removeTaskList)

        # 创建 name_space
        self.name_space_layout = QtWidgets.QGridLayout()
        self.name_space_label = QtWidgets.QLabel('NameSpace: ')
        self.name_space_label.setMaximumWidth(120)
        self.name_space_comboBox = QtWidgets.QComboBox()
        self.name_space_refresh = self.pushButton('Refresh')
        self.name_space_refresh.clicked.connect(self.set_namespace)
        self.name_space_refresh.setMaximumWidth(80)
        self.name_space_layout.addWidget(self.name_space_label, 0, 0, 1, 1)
        self.name_space_layout.addWidget(self.name_space_comboBox, 0, 1, 1, 1)
        self.name_space_layout.addWidget(self.name_space_refresh, 0, 2, 1, 1)

        # panels setting
        self.panels_layout = QtWidgets.QHBoxLayout()
        self.panel_checkBox = QtWidgets.QCheckBox(u'使用自定义相机视窗')
        self.panel_checkBox.setChecked(False)
        self.panels_layout.addWidget(self.panel_checkBox)
        self.panels_layout.addStretch()

        # 创建 button
        self.buttonLayout = QtWidgets.QGridLayout()
        self.addCamera_checkBox = self.checkBox('addCamera', self.camera_off_icon,self.camera_on_icon )
        self.addCamera_checkBox.setChecked(True)
        self.playblase_checkBox = self.checkBox('playblast', self.rec_off_icon, self.rec_on_icon)
        self.singleButton = self.pushButton(name = f'Single')
        self.batchButton = self.redPushButton(name = f'Batch')
        
        self.hideneg45UIButton = self.pushButton(name = f'Hide-45°')
        self.hideUIButton = self.pushButton(name = f'Hide 0°')
        self.hide45UIButton = self.pushButton(name = f'Hide 45°')
        self.showUIButton = self.pushButton(name = f'Show UI')

        self.AddCamreabuttonLayout = QtWidgets.QGridLayout()
        self.AddCamreabuttonLayout.addWidget(self.addCamera_checkBox, 0, 0, 1, 1)
        self.AddCamreabuttonLayout.addWidget(self.playblase_checkBox, 0, 1, 1, 1)
        self.AddCamreabuttonLayout.addWidget(self.singleButton, 0, 2, 1, 1)
        self.AddCamreabuttonLayout.addWidget(self.batchButton, 0, 3, 1, 1)



        self.buttonLayout.addLayout(self.AddCamreabuttonLayout, 2, 0, 1, 6)

        self.buttonLayout.addWidget(self.hideneg45UIButton, 3, 0, 1, 1)
        self.buttonLayout.addWidget(self.hideUIButton,      3, 1, 1, 1)
        self.buttonLayout.addWidget(self.hide45UIButton,    3, 2, 1, 1)
        self.buttonLayout.addWidget(self.showUIButton,      3, 3, 1, 3)

        # 创建进度条
        self.progress_layout = QtWidgets.QHBoxLayout()
        self.progress = QtWidgets.QProgressBar()
        self.progress.setMinimum(0)
        self.progress.setMaximum(100)
        
        # 排列控件到 mainLayout
        # self.mainLayout.addLayout(config_grid)
        self.mainLayout.addLayout(self.parameter_layout)
        self.mainLayout.addWidget(self.fileListView)
        self.mainLayout.addLayout(self.name_space_layout)
        self.mainLayout.addLayout(self.panels_layout)
        self.mainLayout.addLayout(self.buttonLayout)
        self.mainLayout.addLayout(self.progress_layout)
        
        self.progress_layout.addWidget(self.progress)
        self.progress.setHidden(1)

        
    def connect_signals(self):
        # self.actionLoad.triggered.connect(self.getFilePath)
        # self.actionExport.triggered.connect(self.getFilePath)


        self.save_other_checkBox.stateChanged.connect(self.chenge_button_str)     
        self.f_cam_checkBox.stateChanged.connect(self.chenge_cam_checkbox)     
        self.b_cam_checkBox.stateChanged.connect(self.chenge_cam_checkbox)     
        self.l_cam_checkBox.stateChanged.connect(self.chenge_cam_checkbox)     
        self.r_cam_checkBox.stateChanged.connect(self.chenge_cam_checkbox)     
        self.lenght_slider.valueChanged.connect(self.update_slider)
        self.rotate_slider.valueChanged.connect(self.update_slider)
        self.singleButton.clicked.connect(self.add_capture_camera)
        self.batchButton.clicked.connect(self.run)
        self.hideneg45UIButton.clicked.connect(main.hide_neg45_ui)
        self.hideUIButton.clicked.connect(main.hide_ui)
        self.hide45UIButton.clicked.connect(main.hide_45_ui)
        self.showUIButton.clicked.connect(main.show_ui)

    def load_export_fun(self):
        save_path = cmds.fileDialog2(fm=0, ff="Filtered Files (*.json)", dialogStyle=2)
        save_path = save_path[0] if save_path else None

    def set_namespace(self):
        default_namespaces = ['']
        all_namespaces = cmds.namespaceInfo(listOnlyNamespaces=True, recurse=True) or []
        user_namespaces = [
            ns for ns in all_namespaces
            if ns not in ["UI", "shared"]  # 排除 Maya 内部命名空间
        ]
        self.name_space_comboBox.clear()
        self.name_space_comboBox.addItems(default_namespaces + user_namespaces)

    def get_namespace(self):
        return self.name_space_comboBox.currentText()

    def get_panel_active(self):
        return self.panel_checkBox.isChecked()

    def chenge_iconbox(self):
        """
        设置渲染模式
        """
        white_model_q = cmds.iconTextCheckBox('white_model', q=1, value=1)
        wire_model_q = cmds.iconTextCheckBox('wire_model', q=1, value=1)
        Material_model_q = cmds.iconTextCheckBox('Material_model', q=1, value=1)

        if white_model_q:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True, displayAppearance="smoothShaded", useDefaultMaterial=0)
        else:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True, displayAppearance="wireframe")
                
        if wire_model_q:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True, displayAppearance="smoothShaded", wireframeOnShaded=1, useDefaultMaterial=0)
        else:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True, wireframeOnShaded=0)

        if Material_model_q:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True,  displayAppearance="smoothShaded", displayTextures=1, useDefaultMaterial=0)
        else:
            for i in cmds.getPanel( type='modelPanel'):
                cmds.modelEditor(i, edit=True, displayTextures=0)
        
    def chenge_button_str(self):
        self.save_other_checkBox.setText('Save Scene As' if self.save_other_checkBox.isChecked() else 'Save Scene')

    def add_capture_camera(self):
        user_camera_list = main.get_camera_list_by_panes()
        if self.addCamera_checkBox.isChecked():
            name_space = self.get_namespace()
            current_file = cmds.file(q=True, sn=True)
            if current_file:
                lenght_value, rotate_value = main.create_follow_camera(current_file, name_space=name_space)
                self.lenght_slider.setValue(lenght_value)
                self.rotate_slider.setValue(rotate_value)

        is_user_pane = self.get_panel_active()
        main.playblase_setUI(is_user_pane, user_camera_list)
        if self.playblase_checkBox.isChecked():
            # main.playblase_setUI(is_user_pane)
            self.chenge_iconbox()
            cmds.refresh()
            cmds.dotnetAutoViewportRecoderUI()

    def chenge_cam_checkbox(self, state):
        cam_value = self.lenght_slider.value()
        sender = self.sender()
        for camera_offset, cb in zip(self.camera_offsets, [self.f_cam_checkBox, self.b_cam_checkBox, self.l_cam_checkBox, self.r_cam_checkBox]):
            if sender == cb:
                if cmds.objExists(camera_offset):
                    cam_grp = camera_offset.replace('_offset','_grp')
                    cb_value = self.lenght_slider.value()
                    if state==0:
                        cmds.parent(camera_offset, cam_grp)
                        cmds.setAttr(f'{camera_offset}.t', 0, 0, cb_value)
                        cmds.setAttr(f'{camera_offset}.r', 0, 0, 0)
                    elif state==2:
                        cmds.parent(camera_offset, w=1)

    def update_slider(self, value):
        '''
        改变distance 和 rotate 的值执行
        '''
        sender = self.sender()
        # print(self.f_cam_checkBox.isChecked())
        if sender == self.lenght_slider:
            for camera_offset, cb in zip(self.camera_offsets, [self.f_cam_checkBox, self.b_cam_checkBox, self.l_cam_checkBox, self.r_cam_checkBox]):
                if cmds.objExists(camera_offset):
                    if not cb.isChecked():
                        cmds.setAttr(f'{camera_offset}.translateZ', value)
                        cmds.setAttr(f'{main.CAMERAALLGRP}.selectHandleZ', value)
                        
        elif sender == self.rotate_slider:
            point_skip, orient_skip, ro_attr = main.get_up_axis_info()
            if cmds.objExists(main.CAMERAALLGRP):
                cmds.setAttr(f'{main.CAMERAALLGRP}.{ro_attr}', value)
                cmds.setAttr(f'{main.CAMERAALLGRP}.selectHandleY', value)
                
    def refresh_slider_value(self):
        if cmds.objExists(main.CAMERAALLGRP):
            self.lenght_slider.setValue(cmds.getAttr(f'{main.CAMERAALLGRP}.selectHandleZ'))
            self.rotate_slider.setValue(cmds.getAttr(f'{main.CAMERAALLGRP}.selectHandleY'))


    def resizeMainWindow(self, width, height):
        # CREATE ANIMATION
        self.animation = QtCore.QPropertyAnimation(self, b"size")
        self.animation.setDuration(500)
        self.animation.setEndValue(QtCore.QSize(width, height))
        self.animation.setEasingCurve(QtCore.QEasingCurve.InOutQuad)
        self.animation.start()

    def setInput(self, dirs_or_files):
        '''
        设置输出fbx的路径
        '''
        input_maya_files = []
        for path in dirs_or_files:
            if os.path.isfile(path):
                input_maya_files.append(path)
                continue
            for root, dirs, files in os.walk(path):
                for f in files:
                    name = os.path.join(root, f)
                    input_maya_files.append(name)
        return input_maya_files
    
    def removeTaskList(self):
        """[清空所有Item]
        """
        list_wideget = self.fileListView
        for i in range(list_wideget.count()):
            item = list_wideget.takeItem(0)
            list_wideget.removeItemWidget(item)
            del item
        self.resizeMainWindow(850, 460)
        self.tab_Widget.removeTab(1)
        # self.switch_progress()

    def setTaskQueue(self, input_maya_files):
        """[设置任务队列]

        :param input_maya_files: [description]
        :type input_maya_files: [type]
        """
        self.removeTaskList()
        self.task_Item_list = list()
        for f in input_maya_files:
            icon = QtGui.QIcon(self.file_icon)
            list_wideget_item = QtWidgets.QListWidgetItem()
            list_wideget_item.setIcon(icon)
            list_wideget_item.setText(f)
            self.list_wideget = self.fileListView
            self.list_wideget.addItem(list_wideget_item)
            self.task_Item_list.append(list_wideget_item)
        self.list_wideget.doubleClicked.connect(lambda: self.__openErrorInfo(self.list_wideget))


    def getFilePath(self):
        """[把选择的文件列表传递给 self.dirs_or_files]
        """
        self.dirs_or_files = []
        def __get_file_list(files):
            self.dirs_or_files = files
            file_list = self.setInput(self.dirs_or_files)
            self.setTaskQueue(file_list)
            high = len(file_list) * 15
            self.ani_size = (1024, 400 + high)
            self.resizeMainWindow(1024, 400 + high)

        self.open_ui = MWidgets.DirectoryFileDialog()
        self.open_ui.setNameFilters(["FBX Files (*.ma)"])
        self.open_ui.filesSelected.connect(__get_file_list)
        self.open_ui.show()

    def closeEvent(self, event):
        """[关闭窗口时，关闭所有异常信息]"""
        # cmds.deleteUI(self.window)
        main.set_user_workspace_layout()
        if cmds.window(self.window, q=1, exists=1):
            cmds.deleteUI(self.window, window=1)



    def __removeExcList(self):
        """[summary]

        :param info: [description]
        :type info: [type]
        """
        layout = self.infoMainLayout
        for i in reversed(range(layout.count())):
            item = layout.itemAt(i)
            if item.widget():
                item.widget().close()
                layout.removeWidget(item.widget())
            else:
                layout.removeItem(item)

    def __excBox(self, name, info):
        """[创建异常信息box]

        :param info: [description]
        :type info: [type]
        """
        box = MWidgets.QCollapseBox()
        box.setBoxName(name)
        box.setExpandBox(False)

        text = QtWidgets.QTextEdit()
        text.setPlainText(info)
        text.setReadOnly(True)

        box.addWidget(text)
        self.infoMainLayout.addWidget(box)
        self.tab_Widget.addTab(self.info_tap, "ErrorBox") # 添加ErrorBox
        return box

    def __openErrorInfo(self, list_wideget):
        if self.excIndexBoxDict:
            exc_index_box_dict = self.excIndexBoxDict
            selected_item = list_wideget.selectedItems()[0]

            # NOTE 返回一个QModelIndex, QModelIndex是一个二维数据结构 .row()横向 .column()纵向 这里面是用的row
            index = list_wideget.indexFromItem(selected_item).row()
            if index in exc_index_box_dict.keys():
                # NOTE 关闭所有box
                [k.setExpandBox(False) for k in exc_index_box_dict.values()]
                # NOTE 切换到tab1
                self.tab_Widget.setCurrentIndex(1)
                box = exc_index_box_dict[index]
                box.setExpandBox(True)

    def execute(self, func):
        # NOTE 清楚错误信息里面的组件
        self.__removeExcList()
        task_Item_list = [self.fileListView.item(k) for k in range(self.fileListView.count())]
        if task_Item_list:
            self.excIndexBoxDict = dict()
            for num, item in enumerate(task_Item_list):
                amount = float(num + 1) / len(task_Item_list) * 100
                self.progress.setValue(amount)
                file_path = item.text()
                try:
                    self.chenge_iconbox()
                    save_other_switch = self.save_other_checkBox.isChecked()
                    camera_switch = True if self.addCamera_checkBox.isChecked() else False
                    playblase_switch = True if self.playblase_checkBox.isChecked() else False
                    white_model_q = cmds.iconTextCheckBox('white_model', q=1, value=1)
                    wire_model_q = cmds.iconTextCheckBox('wire_model', q=1, value=1)
                    Material_model_q = cmds.iconTextCheckBox('Material_model', q=1, value=1)
                    offset = self.lenght_slider.value()
                    rotate = self.rotate_slider.value()
                    func(
                        file_path,
                        save_other_switch,
                        camera_switch,
                        playblase_switch,
                        white_model_q,
                        wire_model_q,
                        Material_model_q,
                        offset,
                        rotate
                    ) #  在这里传入 地址参数
                    item.setIcon(QtGui.QIcon(self.success_icon))
                except Exception as ex:
                    # NOTE 捕获异常并报错log文件到当前路径
                    excBox = self.__excBox(file_path, traceback.format_exc())
                    self.excIndexBoxDict[num] = excBox
                    item.setIcon(QtGui.QIcon(self.failed_icon))
            # NOTE 这需要在创建BOX之后发生，否则将无法正常工作
            self.infoMainLayout.setDirection(QtWidgets.QBoxLayout.TopToBottom)
            self.infoMainLayout.addStretch(1)
            self.progress.setValue(0)
            

    def switch_progress(self):
        if self.progress.isHidden():
            self.progress.setHidden(0)
        else:
            self.progress.setHidden(1)

    def run(self):
        thread1 = threading.Thread(target=self.switch_progress)
        thread1.start()
        
        thread2 = threading.Thread(target=self.execute(main.open_file_add_camera))
        thread2.start()

        thread3 = threading.Thread(target=self.switch_progress)
        thread3.start()

        thread1.join()
        thread2.join()
        thread3.join()

    def changedFileLiseTab(self):
        [k.buttonClose() for k in self.excIndexBoxDict.values()]
        self.resizeMainWindow(self.ani_size[0],self.ani_size[1])

    @classmethod
    def showUI(cls):
        if cmds.window(UINAME, exists=True):
            cmds.deleteUI(UINAME)
        win = cls()
        win.show()

        
