import maya.cmds as cmds
from functools import wraps
import json
import os
import random
from Qt import QtWidgets


__vision__ = "V1.0"
_QPushButton_common_qss = """
QPushButton
{{
    font-family : Comic Sans MS;
    font-size : {fsize}px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb{bgc_0}, stop:1 rgb{bgc_1});
    border-radius: 10px;
    padding-top : 0px;
    padding-bottom : 0px;
    padding-left : 0px;
    padding-right : 0px;
}}

QPushButton:hover
{{
    color : rgb{hlc};
}}

QPushButton:pressed,
QPushButton:on
{{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb{pressed_bgc_0}, stop:1 rgb{pressed_bgc_1});
    padding-top : 5px;
    color : rgb{pressed_fc};
}}
"""


class CacheDataWindow():
    data_file_name = "select_cache_data.json"
    win_name = "CacheWin"
    item_count = 20
    __item_count_string = "__item_count"
    max_number_rows = 20

    def __init__(self):
        self.__sav_qss = _QPushButton_common_qss.format(
                                fsize=20,
                                bgc_0="(103, 107, 60)", bgc_1="(83, 84, 36)",
                                hlc="(255, 255, 0)", 
                                pressed_bgc_0="(70, 71, 23)", pressed_bgc_1="(98, 99, 51)",
                                pressed_fc="(255, 255, 0)")
        self.__sel_qss = _QPushButton_common_qss.format(
                                fsize=20,
                                bgc_0="(53, 107, 60)", bgc_1="(33, 84, 36)",
                                hlc="(0, 255, 0)", 
                                pressed_bgc_0="(20, 71, 23)", pressed_bgc_1="(48, 99, 51)",
                                pressed_fc="(0, 255, 0)")
        self.__add_qss = _QPushButton_common_qss.format(
                                fsize=30,
                                bgc_0="(53, 107, 110)", bgc_1="(33, 84, 86)",
                                hlc="(0, 255, 255)", 
                                pressed_bgc_0="(20, 71, 73)", pressed_bgc_1="(48, 99, 101)",
                                pressed_fc="(0, 255, 255)")
        self.__sub_qss = _QPushButton_common_qss.format(
                                fsize=30,
                                bgc_0="(103, 57, 60)", bgc_1="(83, 34, 36)",
                                hlc="(255, 0, 0)", 
                                pressed_bgc_0="(70, 21, 23)", pressed_bgc_1="(98, 49, 51)",
                                pressed_fc="(255, 0, 0)")
        self.data_file_path = self.define_data_file(self.data_file_name)
        self.current_index = 1
        self.cache_data = {}
        self.load_data()

    def showUI(self):
        self.create_ui()
        self.update_text()

    @staticmethod
    def changeStyle(control_name, qt_components, style_qss):
        def long(val):
            import sys
            if sys.version_info[0] >= 3:
                import builtins
                return builtins.int(val)
            else:
                import __builtin__ as builtins
                return builtins.long(val)
        
        import maya.OpenMayaUI as OpenMayaUI
        from shiboken2 import wrapInstance

        ptr = OpenMayaUI.MQtUtil.findControl(control_name)
        widget = wrapInstance(long(ptr), qt_components)
        widget.setStyleSheet(style_qss)

    def try_eval(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as message:
                pass
        return wrapper

    @try_eval
    def eval(self, script):
        return cmds.python(script)

    @staticmethod
    def define_data_file(json_name):
        data_file = os.path.join(os.getenv("MAYA_APP_DIR"), json_name)
        if os.altsep:
            data_file = data_file.replace(os.sep, os.altsep)
        return data_file

    def load_data(self):
        if not os.path.isfile(self.data_file_path):
            self.current_index = self.item_count
            return False
        with open(self.data_file_path, "r") as file:
            self.cache_data = json.load(file)
        for key in self.cache_data.keys():
            if key == self.__item_count_string:
                self.current_index = self.cache_data[self.__item_count_string]
                # if self.current_index <= 0:
                #     self.current_index = 1
                continue
            cmds.python("{0} = {1}".format(key, self.cache_data[key][1]))
        return True

    def update_text(self):
        for key in self.cache_data.keys():
            if key == self.__item_count_string:
                continue
            cmds.textField("{0}_TF".format(key), e=True, tx=self.cache_data[key][0])
        return True

    def close_command(self):
        cache_data = {}
        cache_data[self.__item_count_string] = self.current_index
        for index in range(self.current_index):
            key_name = "_CHCHE_{0}".format(index)
            tf_string = cmds.textField("{0}_TF".format(key_name), q=True, tx=True)
            cache_data[key_name] = [tf_string, self.eval(key_name)]
        with open(self.data_file_path, "w") as file:
            json.dump(cache_data, file)

    @staticmethod
    def txt_menu_cmd(txt_fed_contrl, txt_string):
        bgc = [random.uniform(0.4, 1.0) for _ in range(3)]
        cmds.textField(txt_fed_contrl, edit=True, text=txt_string, bgc=bgc)

    @staticmethod
    def txt_menu_clear(txt_fed_contrl):
        cmds.textField(txt_fed_contrl, edit=True, text="", bgc=[0,0,0])

    def create_menu_item(self, txt_fed_contrl, index):
        pop_menu = cmds.popupMenu(parent=txt_fed_contrl)
        cmds.menuItem(label="Clear", p=pop_menu, command=lambda x: self.txt_menu_clear(txt_fed_contrl))
        cmds.menuItem(label="objs", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "objs_{}".format(index+1)))
        cmds.menuItem(label="vtx", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "vtx_{}".format(index+1)))
        cmds.menuItem(label="Item", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Item_{}".format(index+1)))
        cmds.menuItem(label="Shape", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Shape_{}".format(index+1)))
        cmds.menuItem(label="Ctrls", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Ctrls_{}".format(index+1)))
        cmds.menuItem(label="BlendShape", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "BlendShape_{}".format(index+1)))
        cmds.menuItem(label="Joint", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Joint_{}".format(index+1)))
        cmds.menuItem(label="LSRTC", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LSRTC_{}".format(index+1)))
        cmds.menuItem(label="LSRTMC", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LSRTMC_{}".format(index+1)))
        cmds.menuItem(label="NESTCOSNT", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "NESTCOSNT_{}".format(index+1)))
        cmds.menuItem(label="Constrant_Objs", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Constrant_Objs_{}".format(index+1)))
        cmds.menuItem(label="LOD0_BindJoint", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LOD0_BindJoint_{}".format(index+1)))
        cmds.menuItem(label="LOD1_BindJoint", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LOD1_BindJoint_{}".format(index+1)))
        cmds.menuItem(label="LOD2_BindJoint", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LOD2_BindJoint_{}".format(index+1)))
        cmds.menuItem(label="RIG_SKELETON", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "RIG_SKELETON_{}".format(index+1)))
        cmds.menuItem(label="BIND_SKELETON", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "BIND_SKELETON_{}".format(index+1)))
        cmds.menuItem(label="LIMB", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "LIMB_{}".format(index+1)))
        cmds.menuItem(label="RIGMESH", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "RIGMESH_{}".format(index+1)))
        cmds.menuItem(label="Facial", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Facial_{}".format(index+1)))
        cmds.menuItem(label="Arm_IKFK", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Arm_IKFK_{}".format(index+1)))
        cmds.menuItem(label="Leg_IKFK", command=lambda x: self.txt_menu_cmd(txt_fed_contrl, "Leg_IKFK_{}".format(index+1)))

    def create_item(self, index):
        frm_lay = cmds.formLayout("_CHCHE_{0}_formLayout".format(index), p=self.layout_item)
        txt_fed = cmds.textField("_CHCHE_{0}_TF".format(index), hlc=[1,0,0])
        sav_command = "_CHCHE_{0} = cmds.ls(sl=True);import random;cmds.textField('{1}', edit=True, bgc=[random.uniform(0.4, 1.0) for _ in range(3)])".format(index, txt_fed)
        btn_sav = cmds.button(l="Save {0:02d}".format(index+1), ann="Records the selected object.",
                    c=lambda x: self.eval(sav_command))
        btn_sub = cmds.button(l="-", bgc=[0.3,0.1,0.0], ann="Reselect cache objects.",
                    c=lambda x: self.eval("cmds.select(_CHCHE_{0}, d=True)".format(index)))
        btn_sel = cmds.button(l="select", bgc=[0.3,0.3,0.5], ann="Select the cache object.",
                    c=lambda x: self.eval("cmds.select(_CHCHE_{0})".format(index)))
        btn_add = cmds.button(l="+", bgc=[0.5,0.5,0.0], ann="Adding cache objects.",
                    c=lambda x: self.eval("cmds.select(_CHCHE_{0}, add=True)".format(index)))
        sep = cmds.separator(style="in")
        cmds.setParent("..")
        cmds.formLayout(frm_lay, e=True,
            an=[
                [sep, "top"]
            ],
            af=[
                [txt_fed, "top", 1],
                [btn_sav, "top", 1],
                [txt_fed, "left", 1],
                [btn_sav, "right", 1],
                [btn_sub, "left", 1],
                [btn_add, "right", 1],
                [sep, "left", 1],
                [sep, "right", 1],
                [sep, "bottom", 1]
            ],
            ap=[
                [txt_fed, "right", 0, 70],
                [btn_sub, "right", 0, 30],
                [btn_sel, "right", 0, 70]
            ],
            ac=[
                [btn_sub, "top", 8, btn_sav],
                [btn_sel, "top", 8, btn_sav],
                [btn_add, "top", 8, btn_sav],
                [btn_sav, "left", 1, txt_fed],
                [btn_sel, "left", 1, btn_sub],
                [btn_add, "left", 1, btn_sel],
                [btn_sub, "bottom", 2, sep],
                [btn_sel, "bottom", 2, sep],
                [btn_add, "bottom", 2, sep]
            ]
        )
        self.create_menu_item(txt_fed, index)

        try:
            self.changeStyle(btn_sav, QtWidgets.QPushButton, self.__sav_qss)
            self.changeStyle(btn_sub, QtWidgets.QPushButton, self.__sub_qss)
            self.changeStyle(btn_sel, QtWidgets.QPushButton, self.__sel_qss)
            self.changeStyle(btn_add, QtWidgets.QPushButton, self.__add_qss)
        except BaseException as message:
            print(message)

    def add_items(self):
        for id in range(self.current_index):
            self.create_item(id)

    def add_one_item(self):
        self.create_item(self.current_index)
        cmds.window(self.win_name, e=True, h=1)
        self.current_index += 1

    def sub_one_item(self):
        form_name = "_CHCHE_{0}_formLayout".format(self.current_index-1)
        if cmds.formLayout(form_name, q=True, ex=True):
            cmds.deleteUI(form_name)
            self.current_index -= 1
            cmds.window(self.win_name, e=True, h=1)

    def reset_items(self):
        if cmds.rowColumnLayout(self.layout_item, q=True, ex=True):
            cmds.deleteUI(self.layout_item)
            self.load_data()
            self.outside_the_window()
            self.update_text()
            cmds.window(self.win_name, e=True, h=1)

    def create_editor_btn(self):
        frm_lay = cmds.formLayout()
        def_sbtn = cmds.symbolButton(image="defaultInfluenceList.png",c=lambda x: self.reset_items(), ann="The tool resets to its state at startup.")
        add_sbtn = cmds.symbolButton(image="expandInfluenceList.png", c=lambda x: self.add_one_item(), ann="Expand a item.")
        sub_sbtn = cmds.symbolButton(image="retractInfluenceList.png", c=lambda x: self.sub_one_item(), ann="Remove a item.")
        cmds.setParent("..")
        cmds.formLayout(frm_lay, e=True,
            af=[
                [def_sbtn, "top", 1],
                [def_sbtn, "left", 1],
                [add_sbtn, "top", 1],
                [sub_sbtn, "top", 1],
                [sub_sbtn, "right", 1]
            ],
            ap=[
                [def_sbtn, "right", 0, 30],
                [add_sbtn, "right", 0, 66]
            ],
            ac=[
                [add_sbtn, "left", 1, def_sbtn],
                [sub_sbtn, "left", 1, add_sbtn]
            ]
        )

    def outside_the_window(self):
        self.layout_item = cmds.rowColumnLayout(numberOfRows=self.max_number_rows, adj=True)
        self.add_items()

    def create_ui(self):
        if cmds.window(self.win_name, q=True, ex=True):
            cmds.deleteUI(self.win_name, wnd=True)
        cmds.window(self.win_name, t="Select and record {0}".format(__vision__),
                    bgc=[0.3,0.3,0.3], w=200, s=True, cc=self.close_command)
        cmds.columnLayout(adj=True)
        self.create_editor_btn()
        cmds.separator(style="in")
        if cmds.windowPref(self.win_name, q=True, ex=True):
            cmds.windowPref(self.win_name, r=True)
        cmds.showWindow(self.win_name)
        self.outside_the_window()


def launch():
    return CacheDataWindow().create_ui()


