#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Import built-in modules

# Import third-party modules
from maya import cmds
from maya import mel

# Import local modules

END_SUFFIX = '_tmpDup'
END_SUFFIX_GROUP = '{}Group'.format(END_SUFFIX)


def get_obj_type(obj):
    obj_type = cmds.objectType(obj)
    if obj_type == 'transform':
        shapes = cmds.listRelatives(obj, shapes=True) or []
        if shapes:
            obj_type = cmds.objectType(shapes[0])
    return obj_type


def get_end_controller(obj):
    obj_type = cmds.objectType(obj)
    obj_shape_type = get_obj_type(obj)
    sub_objs = cmds.listRelatives(obj, allDescendents=True, fullPath=True, type=obj_type) or []
    if not sub_objs:
        return obj
    sub_objs = sorted(sub_objs, key=len, reverse=True)
    for sub_obj in sub_objs:
        if sub_obj.endswith(END_SUFFIX):
            continue
        if get_obj_type(sub_obj) == obj_shape_type:
            return sub_obj
    return obj


def get_controller_chains(obj):
    chains = [obj]
    obj_type = cmds.objectType(obj)
    obj_shape_type = get_obj_type(obj)
    sub_objs = cmds.listRelatives(obj, allDescendents=True, fullPath=True, type=obj_type) or []
    if not sub_objs:
        return chains
    sub_objs = sorted(sub_objs, key=len, reverse=False)
    for sub_obj in sub_objs:
        if get_obj_type(sub_obj) == obj_shape_type:
            chains.append(sub_obj)
    return chains


def get_obj_children_list(obj):
    all_shapes = cmds.listRelatives(obj, shapes=True, fullPath=True) or []
    all_sub_objs = cmds.listRelatives(obj, allDescendents=True, fullPath=True) or []
    for sub_obj in all_sub_objs:
        if sub_obj in all_shapes:
            continue
        shapes = cmds.listRelatives(sub_obj, shapes=True, fullPath=True) or []
        if shapes:
            all_shapes.extend(shapes)
    sub_objs = [sub_obj for sub_obj in all_sub_objs if sub_obj not in all_shapes]
    return sub_objs


def get_simple_node_name(node_name):
    node_name = cmds.ls(node_name)[0]
    if '|' in node_name:
        node_name = node_name.replace('|', '_')
    if ':' in node_name:
        node_name = node_name.replace(':', '_')
    return node_name


def get_dup_name(obj):
    node_name = get_simple_node_name(obj)
    dup_name = '{}{}'.format(node_name, END_SUFFIX)
    return dup_name


def get_dup_group_name(obj):
    node_name = get_simple_node_name(obj)
    dup_group = '{}{}'.format(node_name, END_SUFFIX_GROUP)
    return dup_group


def duplicate_obj_only(obj):
    dup_name = get_dup_name(obj)
    new_obj = cmds.duplicate(obj, renameChildren=True, returnRootsOnly=True, name=dup_name)[0]
    new_obj = cmds.ls(new_obj, long=True)[0]
    children_list = get_obj_children_list(new_obj)
    if children_list:
        cmds.delete(children_list)
    return new_obj






